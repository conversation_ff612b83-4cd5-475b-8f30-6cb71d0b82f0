import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🔍 Service de diagnostic pour le Super Admin
class SuperAdminDiagnosticService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔍 Diagnostic complet de l'état Super Admin
  static Future<Map<String, dynamic>> fullDiagnostic() async {
    try {
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 🔍 === DIAGNOSTIC COMPLET ===');

      final result = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'firebase_auth': {},
        'firestore_user': {},
        'permissions': {},
        'recommendations': [],
      };

      // 1. Vérifier Firebase Auth
      final authDiagnostic = await _diagnoseFirebaseAuth();
      result['firebase_auth'] = authDiagnostic;

      // 2. Vérifier Firestore User Document
      if (authDiagnostic['user_uid'] != null) {
        final firestoreDiagnostic = await _diagnoseFirestoreUser(authDiagnostic['user_uid']);
        result['firestore_user'] = firestoreDiagnostic;

        // 3. Vérifier les permissions
        final permissionsDiagnostic = await _diagnosePermissions(
          authDiagnostic['user_uid'],
          firestoreDiagnostic,
        );
        result['permissions'] = permissionsDiagnostic;
      }

      // 4. Générer des recommandations
      result['recommendations'] = _generateRecommendations(result);

      _printDiagnosticReport(result);
      return result;

    } catch (e) {
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] ❌ Erreur diagnostic: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔐 Diagnostic Firebase Auth
  static Future<Map<String, dynamic>> _diagnoseFirebaseAuth() async {
    final currentUser = _auth.currentUser;
    
    return {
      'is_authenticated': currentUser != null,
      'user_uid': currentUser?.uid,
      'user_email': currentUser?.email,
      'email_verified': currentUser?.emailVerified,
      'creation_time': currentUser?.metadata.creationTime?.toIso8601String(),
      'last_sign_in': currentUser?.metadata.lastSignInTime?.toIso8601String(),
      'expected_email': '<EMAIL>',
      'email_matches': currentUser?.email == '<EMAIL>',
    };
  }

  /// 📄 Diagnostic Firestore User Document
  static Future<Map<String, dynamic>> _diagnoseFirestoreUser(String uid) async {
    try {
      final userDoc = await _firestore.collection('users').doc(uid).get();
      
      if (!userDoc.exists) {
        return {
          'document_exists': false,
          'error': 'Document utilisateur non trouvé dans Firestore',
        };
      }

      final userData = userDoc.data()!;
      
      return {
        'document_exists': true,
        'role': userData['role'],
        'email': userData['email'],
        'status': userData['status'],
        'created_at': userData['created_at']?.toString(),
        'created_by': userData['created_by'],
        'is_super_admin_role': userData['role'] == 'super_admin',
        'all_fields': userData.keys.toList(),
        'document_data': userData,
      };

    } catch (e) {
      return {
        'document_exists': false,
        'error': 'Erreur lecture Firestore: $e',
      };
    }
  }

  /// 🔑 Diagnostic des permissions
  static Future<Map<String, dynamic>> _diagnosePermissions(
    String uid,
    Map<String, dynamic> firestoreData,
  ) async {
    final permissions = <String, dynamic>{
      'can_create_admin_compagnie': false,
      'can_create_admin_agence': false,
      'can_access_super_admin_dashboard': false,
      'issues': [],
    };

    // Vérifier si peut créer admin compagnie
    if (firestoreData['document_exists'] == true && 
        firestoreData['role'] == 'super_admin') {
      permissions['can_create_admin_compagnie'] = true;
      permissions['can_create_admin_agence'] = true;
      permissions['can_access_super_admin_dashboard'] = true;
    } else {
      if (firestoreData['document_exists'] != true) {
        permissions['issues'].add('Document Firestore manquant');
      }
      if (firestoreData['role'] != 'super_admin') {
        permissions['issues'].add('Rôle incorrect: ${firestoreData['role']} (attendu: super_admin)');
      }
    }

    return permissions;
  }

  /// 💡 Générer des recommandations
  static List<String> _generateRecommendations(Map<String, dynamic> diagnostic) {
    final recommendations = <String>[];
    final auth = diagnostic['firebase_auth'] as Map<String, dynamic>;
    final firestore = diagnostic['firestore_user'] as Map<String, dynamic>;
    final permissions = diagnostic['permissions'] as Map<String, dynamic>;

    if (auth['is_authenticated'] != true) {
      recommendations.add('🔐 Se connecter avec le compte Super Admin (<EMAIL>)');
    }

    if (auth['email_matches'] != true) {
      recommendations.add('📧 Utiliser l\'email Super Admin correct: <EMAIL>');
    }

    if (firestore['document_exists'] != true) {
      recommendations.add('📄 Créer le document Firestore pour le Super Admin');
      recommendations.add('🔧 Exécuter: await createSuperAdminFirestoreDocument()');
    }

    if (firestore['is_super_admin_role'] != true) {
      recommendations.add('🏷️ Corriger le rôle dans Firestore: role = "super_admin"');
    }

    if (permissions['can_create_admin_compagnie'] != true) {
      recommendations.add('⚠️ Résoudre les problèmes de permissions avant de créer des utilisateurs');
    }

    return recommendations;
  }

  /// 📊 Afficher le rapport de diagnostic
  static void _printDiagnosticReport(Map<String, dynamic> diagnostic) {
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 📊 === RAPPORT DE DIAGNOSTIC ===');
    
    final auth = diagnostic['firebase_auth'] as Map<String, dynamic>;
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 🔐 Firebase Auth:');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Connecté: ${auth['is_authenticated']}');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - UID: ${auth['user_uid']}');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Email: ${auth['user_email']}');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Email correct: ${auth['email_matches']}');

    final firestore = diagnostic['firestore_user'] as Map<String, dynamic>;
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 📄 Firestore:');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Document existe: ${firestore['document_exists']}');
    if (firestore['document_exists'] == true) {
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Rôle: ${firestore['role']}');
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Status: ${firestore['status']}');
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Super Admin: ${firestore['is_super_admin_role']}');
    }

    final permissions = diagnostic['permissions'] as Map<String, dynamic>;
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 🔑 Permissions:');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Peut créer admin: ${permissions['can_create_admin_compagnie']}');
    debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   - Problèmes: ${permissions['issues']}');

    final recommendations = diagnostic['recommendations'] as List<String>;
    if (recommendations.isNotEmpty) {
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 💡 Recommandations:');
      for (final rec in recommendations) {
        debugPrint('[SUPER_ADMIN_DIAGNOSTIC]   $rec');
      }
    }

    debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 📊 === FIN RAPPORT ===');
  }

  /// 🔧 Créer le document Firestore pour le Super Admin
  static Future<bool> createSuperAdminFirestoreDocument() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        debugPrint('[SUPER_ADMIN_DIAGNOSTIC] ❌ Aucun utilisateur connecté');
        return false;
      }

      if (currentUser.email != '<EMAIL>') {
        debugPrint('[SUPER_ADMIN_DIAGNOSTIC] ❌ Email incorrect: ${currentUser.email}');
        return false;
      }

      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] 🔧 Création document Firestore...');

      final userData = {
        'email': currentUser.email,
        'role': 'super_admin',
        'status': 'actif',
        'nom': 'Super',
        'prenom': 'Admin',
        'phone': '+216 70 000 000',
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'created_by': 'system',
        'isLegitimate': true,
        'accessLevel': 'production',
      };

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .set(userData);

      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] ✅ Document Firestore créé avec succès');
      return true;

    } catch (e) {
      debugPrint('[SUPER_ADMIN_DIAGNOSTIC] ❌ Erreur création document: $e');
      return false;
    }
  }

  /// 🧪 Test rapide des permissions
  static Future<bool> quickPermissionTest() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      return userDoc.exists && userDoc.data()!['role'] == 'super_admin';
    } catch (e) {
      return false;
    }
  }
}
