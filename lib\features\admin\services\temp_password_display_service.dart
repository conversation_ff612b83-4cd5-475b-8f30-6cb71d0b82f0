import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 📋 Service temporaire pour afficher les mots de passe quand l'email ne fonctionne pas
class TempPasswordDisplayService {
  
  /// 📋 Afficher le mot de passe dans une popup
  static void showPasswordDialog({
    required BuildContext context,
    required String email,
    required String password,
    required String role,
    String? compagnieNom,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.key_rounded, color: Colors.green),
              const SizedBox(width: 8),
              const Text('Compte Créé !'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Message d'information
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    border: Border.all(color: Colors.orange),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '⚠️ L\'email n\'a pas pu être envoyé.\nVoici les informations de connexion :',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Informations du compte
                _buildInfoRow('📧 Email:', email),
                const SizedBox(height: 8),
                _buildPasswordRow(context, '🔑 Mot de passe:', password),
                const SizedBox(height: 8),
                _buildInfoRow('🎭 Rôle:', role),
                if (compagnieNom != null) ...[
                  const SizedBox(height: 8),
                  _buildInfoRow('🏢 Compagnie:', compagnieNom),
                ],
                
                const SizedBox(height: 16),
                
                // Instructions
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    border: Border.all(color: Colors.blue),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '💡 Instructions :\n'
                    '1. Notez ces informations\n'
                    '2. Communiquez-les à l\'utilisateur\n'
                    '3. L\'utilisateur peut se connecter immédiatement',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                // Copier les informations dans le presse-papiers
                final info = 'Email: $email\nMot de passe: $password\nRôle: $role';
                Clipboard.setData(ClipboardData(text: info));
                
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('📋 Informations copiées dans le presse-papiers'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: const Text('📋 Copier'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('✅ Compris'),
            ),
          ],
        );
      },
    );
  }

  /// 📋 Widget pour afficher une ligne d'information
  static Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: SelectableText(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }

  /// 🔑 Widget spécial pour le mot de passe avec bouton copier
  static Widget _buildPasswordRow(BuildContext context, String label, String password) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Expanded(
                  child: SelectableText(
                    password,
                    style: const TextStyle(
                      fontSize: 16,
                      fontFamily: 'monospace',
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: password));
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('🔑 Mot de passe copié !'),
                        backgroundColor: Colors.green,
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  icon: const Icon(Icons.copy_rounded),
                  iconSize: 16,
                  tooltip: 'Copier le mot de passe',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 📧 Afficher un message d'erreur email avec option de réessai
  static void showEmailErrorDialog({
    required BuildContext context,
    required String email,
    required String error,
    VoidCallback? onRetry,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.email_outlined, color: Colors.red),
              const SizedBox(width: 8),
              const Text('Erreur Email'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Impossible d\'envoyer l\'email à :'),
              const SizedBox(height: 8),
              Text(
                email,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontFamily: 'monospace',
                ),
              ),
              const SizedBox(height: 16),
              Text('Erreur : $error'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  border: Border.all(color: Colors.blue),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '💡 Le compte a été créé avec succès.\n'
                  'Seul l\'envoi d\'email a échoué.\n'
                  'Vous pouvez communiquer les informations manuellement.',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
          actions: [
            if (onRetry != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                child: const Text('🔄 Réessayer'),
              ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// 📋 Afficher les informations dans la console de manière formatée
  static void logAccountInfo({
    required String email,
    required String password,
    required String role,
    String? compagnieNom,
  }) {
    debugPrint('\n' + '=' * 60);
    debugPrint('🎯 NOUVEAU COMPTE CRÉÉ');
    debugPrint('=' * 60);
    debugPrint('📧 Email: $email');
    debugPrint('🔑 Mot de passe: $password');
    debugPrint('🎭 Rôle: $role');
    if (compagnieNom != null) {
      debugPrint('🏢 Compagnie: $compagnieNom');
    }
    debugPrint('⚠️  Email non envoyé - Informations à communiquer manuellement');
    debugPrint('=' * 60 + '\n');
  }
}
