import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/agence_service.dart';
import '../../services/compagnie_service.dart';
import '../../services/admin_creation_service.dart';
import '../../services/email_notification_service.dart';
import '../../services/simple_email_service.dart';
import '../../services/rbac_security_service.dart';

/// 🆕 Dialog pour créer un utilisateur directement (Super Admin)
class UserCreateDialog extends StatefulWidget {
  const UserCreateDialog({Key? key}) : super(key: key);

  @override
  State<UserCreateDialog> createState() => _UserCreateDialogState();
}

class _UserCreateDialogState extends State<UserCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomCompletController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _cinController = TextEditingController();
  final _experienceController = TextEditingController();

  String? _selectedRole;
  String? _selectedCompagnieId;
  String? _selectedAgenceId;
  
  List<Map<String, dynamic>> _compagnies = [];
  List<Map<String, dynamic>> _agences = [];
  
  bool _isLoading = false;
  bool _isLoadingAgences = false;

  final List<Map<String, String>> _roles = [
    {'value': 'admin_compagnie', 'label': 'Administrateur de Compagnie'},
    {'value': 'admin_agence', 'label': 'Administrateur d\'Agence'},
    {'value': 'agent_agence', 'label': 'Agent d\'Agence'},
    {'value': 'expert_auto', 'label': 'Expert Automobile'},
  ];

  @override
  void initState() {
    super.initState();
    _loadCompagnies();
  }

  @override
  void dispose() {
    _nomCompletController.dispose();
    _emailController.dispose();
    _telephoneController.dispose();
    _cinController.dispose();
    _experienceController.dispose();
    super.dispose();
  }

  /// 🏢 Charger les compagnies
  Future<void> _loadCompagnies() async {
    try {
      final compagnieService = CompagnieService();
      final compagnies = await compagnieService.getCompagniesForDropdown();
      if (mounted) {
        setState(() {
          _compagnies = compagnies;
        });
      }
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur chargement compagnies: $e');
    }
  }

  /// 🏪 Charger les agences selon la compagnie
  Future<void> _loadAgences(String compagnieId) async {
    setState(() {
      _isLoadingAgences = true;
      _selectedAgenceId = null;
      _agences = [];
    });

    try {
      final agenceService = AgenceService();
      final agences = await agenceService.getAgencesForDropdown(compagnieId: compagnieId);
      if (mounted) {
        setState(() {
          _agences = agences;
          _isLoadingAgences = false;
        });
      }
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur chargement agences: $e');
      if (mounted) {
        setState(() {
          _isLoadingAgences = false;
        });
      }
    }
  }

  /// ✅ Créer l'utilisateur avec Firebase Auth + Firestore
  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // 🔍 Vérifier et corriger les permissions Super Admin
      final permissionsOk = await _ensureSuperAdminPermissions();
      if (!permissionsOk) {
        throw Exception('Impossible de vérifier les permissions Super Admin');
      }
      // Séparer le nom complet en nom et prénom
      final nomComplet = _nomCompletController.text.trim();
      final nameParts = nomComplet.split(' ');
      final prenom = nameParts.isNotEmpty ? nameParts.first : '';
      final nom = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // Générer un mot de passe temporaire sécurisé
      final motDePasseTemporaire = _generateTemporaryPassword();

      Map<String, dynamic> result;

      // Utiliser le service approprié selon le rôle
      if (_selectedRole == 'admin_compagnie') {
        // Obtenir le nom de la compagnie
        final compagnie = _compagnies.firstWhere(
          (c) => c['id'] == _selectedCompagnieId,
          orElse: () => {'nom': 'Compagnie inconnue'},
        );

        result = await AdminCreationService.createLegitimateAdminCompagnie(
          email: _emailController.text.trim().toLowerCase(),
          password: motDePasseTemporaire,
          nom: nom,
          prenom: prenom,
          compagnieId: _selectedCompagnieId!,
          compagnieNom: compagnie['nom'] as String,
          phone: _telephoneController.text.trim().isNotEmpty
              ? _telephoneController.text.trim()
              : null,
          address: null, // Peut être ajouté plus tard
        );
      } else if (_selectedRole == 'admin_agence') {
        result = await AdminCreationService.createLegitimateAdminAgence(
          email: _emailController.text.trim().toLowerCase(),
          password: motDePasseTemporaire,
          nom: nom,
          prenom: prenom,
          compagnieId: _selectedCompagnieId!,
          agenceId: _selectedAgenceId!,
          phone: _telephoneController.text.trim().isNotEmpty
              ? _telephoneController.text.trim()
              : null,
          address: null,
        );
      } else {
        // Pour les autres rôles, utiliser l'ancienne méthode (à améliorer)
        throw Exception('Création de $_selectedRole non encore implémentée avec Firebase Auth');
      }

      if (result['success'] == true) {
        debugPrint('[USER_CREATE] ✅ Utilisateur créé avec succès: ${result['uid']}');
        debugPrint('[USER_CREATE] 📧 Mot de passe temporaire: $motDePasseTemporaire');

        // 📧 Envoyer les identifiants par email (en arrière-plan)
        final emailSent = await _sendAccountCredentialsEmail(
          email: _emailController.text.trim().toLowerCase(),
          userName: nomComplet,
          temporaryPassword: motDePasseTemporaire,
          role: _selectedRole!,
          compagnieId: _selectedCompagnieId,
          agenceId: _selectedAgenceId,
        );

        if (mounted) {
          // 🎯 Afficher le dialog avec les identifiants
          await _showCredentialsDialog(
            email: _emailController.text.trim().toLowerCase(),
            userName: nomComplet,
            temporaryPassword: motDePasseTemporaire,
            emailSent: emailSent,
          );

          if (mounted) {
            Navigator.of(context).pop(true);
          }
        }
      } else {
        throw Exception(result['error'] ?? 'Erreur inconnue');
      }

    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur création: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la création: $e'),
            backgroundColor: ModernTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 🔐 Vérification STRICTE des permissions (RBAC PRODUCTION)
  Future<bool> _ensureSuperAdminPermissions() async {
    try {
      debugPrint('[USER_CREATE] 🔐 Vérification STRICTE permissions RBAC...');

      // Vérification stricte avec le service RBAC
      final securityCheck = await RBACSecurityService.canCreateAdminCompagnie();

      if (securityCheck.isGranted) {
        debugPrint('[USER_CREATE] ✅ Permissions accordées pour ${securityCheck.userRole}');
        return true;
      }

      // ACCÈS REFUSÉ - Aucune correction, aucun contournement
      debugPrint('[USER_CREATE] ❌ ACCÈS STRICTEMENT REFUSÉ');
      debugPrint('[USER_CREATE] 📧 Email: ${securityCheck.userEmail ?? 'N/A'}');
      debugPrint('[USER_CREATE] 🏷️ Rôle: ${securityCheck.currentRole ?? 'N/A'}');
      debugPrint('[USER_CREATE] ⚠️ Raison: ${securityCheck.reason}');

      // Afficher le dialog d'accès refusé
      if (mounted) {
        _showAccessDeniedDialog(
          currentEmail: securityCheck.userEmail ?? 'Inconnu',
          currentRole: securityCheck.currentRole ?? 'Inconnu',
          reason: securityCheck.reason ?? 'Permissions insuffisantes',
          action: securityCheck.action ?? 'Contacter l\'administrateur',
        );
      }

      return false;
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur vérification RBAC: $e');

      if (mounted) {
        _showAccessDeniedDialog(
          currentEmail: 'Erreur',
          currentRole: 'Erreur',
          reason: 'Erreur système lors de la vérification des permissions',
          action: 'Réessayer ou contacter le support technique',
        );
      }

      return false;
    }
  }

  /// 🔑 Générer un mot de passe temporaire sécurisé
  String _generateTemporaryPassword() {
    final random = DateTime.now().millisecondsSinceEpoch;
    final randomString = random.toString().substring(8);
    return 'Temp$randomString!';
  }

  /// 📧 Envoyer les identifiants par email
  Future<bool> _sendAccountCredentialsEmail({
    required String email,
    required String userName,
    required String temporaryPassword,
    required String role,
    String? compagnieId,
    String? agenceId,
  }) async {
    try {
      debugPrint('[USER_CREATE] 📧 Envoi email à: $email');

      // Obtenir les noms de compagnie et agence si disponibles
      String? companyName;
      String? agencyName;

      if (compagnieId != null) {
        final compagnie = _compagnies.firstWhere(
          (c) => c['id'] == compagnieId,
          orElse: () => {},
        );
        companyName = compagnie['nom'] as String?;
      }

      if (agenceId != null) {
        final agence = _agences.firstWhere(
          (a) => a['id'] == agenceId,
          orElse: () => {},
        );
        agencyName = agence['nom'] as String?;
      }

      // Utiliser le service d'email simplifié en priorité
      final success = await SimpleEmailService.sendAccountCreatedEmail(
        userEmail: email,
        userName: userName,
        temporaryPassword: temporaryPassword,
        role: _getRoleDisplayName(role),
        companyName: companyName,
      );

      // Si le service simplifié échoue, essayer l'ancien service
      if (!success) {
        debugPrint('[USER_CREATE] 🔄 Tentative avec service email classique...');
        final fallbackSuccess = await EmailNotificationService.sendAccountCreatedNotification(
          userEmail: email,
          userName: userName,
          temporaryPassword: temporaryPassword,
          role: _getRoleDisplayName(role),
          companyName: companyName,
          agencyName: agencyName,
        );
        return fallbackSuccess;
      }

      if (success) {
        debugPrint('[USER_CREATE] ✅ Email envoyé avec succès');
      } else {
        debugPrint('[USER_CREATE] ❌ Échec envoi email');
      }

      return success;
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur envoi email: $e');
      return false;
    }
  }

  /// 🎯 Afficher le dialog avec les identifiants créés
  Future<void> _showCredentialsDialog({
    required String email,
    required String userName,
    required String temporaryPassword,
    required bool emailSent,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 28),
              SizedBox(width: 12),
              Text('✅ Utilisateur Créé !'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informations utilisateur
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '👤 Informations de Connexion',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Email
                      Row(
                        children: [
                          const Icon(Icons.email, size: 20, color: Colors.blue),
                          const SizedBox(width: 8),
                          const Text('Email: ', style: TextStyle(fontWeight: FontWeight.bold)),
                          Expanded(
                            child: SelectableText(
                              email,
                              style: const TextStyle(fontFamily: 'monospace'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // Mot de passe
                      Row(
                        children: [
                          const Icon(Icons.lock, size: 20, color: Colors.orange),
                          const SizedBox(width: 8),
                          const Text('Mot de passe: ', style: TextStyle(fontWeight: FontWeight.bold)),
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade100,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.orange.shade300),
                              ),
                              child: SelectableText(
                                temporaryPassword,
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Statut email
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: emailSent ? Colors.green.shade50 : Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: emailSent ? Colors.green.shade200 : Colors.orange.shade200,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        emailSent ? Icons.mark_email_read : Icons.email_outlined,
                        color: emailSent ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          emailSent
                              ? '✅ Email envoyé avec succès à $email'
                              : '⚠️ Email non envoyé - Transmettez manuellement les identifiants',
                          style: TextStyle(
                            color: emailSent ? Colors.green.shade700 : Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Instructions
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '📋 Instructions:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text('• L\'utilisateur doit se connecter avec ces identifiants'),
                      Text('• Le mot de passe est temporaire et doit être changé'),
                      Text('• Copiez ces informations si l\'email n\'a pas été envoyé'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton.icon(
              onPressed: () async {
                // Copier les identifiants dans le presse-papier
                final credentials = 'Email: $email\nMot de passe: $temporaryPassword';
                await Clipboard.setData(ClipboardData(text: credentials));

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('📋 Identifiants copiés !'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                }
              },
              icon: const Icon(Icons.copy),
              label: const Text('Copier'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('Terminé'),
            ),
          ],
        );
      },
    );
  }

  /// 🚫 Afficher le dialog d'accès refusé
  void _showAccessDeniedDialog({
    required String currentEmail,
    required String currentRole,
    String? reason,
    String? action,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.block, color: Colors.red, size: 28),
              SizedBox(width: 12),
              Text('🚫 Accès Refusé'),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Message principal
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🔐 Permissions Insuffisantes',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        reason ?? 'Seul le Super Admin peut créer de nouveaux utilisateurs.',
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Informations du compte actuel
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '👤 Compte Actuel:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text('📧 Email: $currentEmail'),
                      Text('🏷️ Rôle: $currentRole'),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Instructions
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '💡 Solution:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(action ?? 'Contacter l\'administrateur système'),
                      if (action == null || action.contains('Super Admin')) ...[
                        const SizedBox(height: 8),
                        const Text('1. Déconnectez-vous de ce compte'),
                        const Text('2. Connectez-vous avec le compte Super Admin'),
                        const Text('3. Email: <EMAIL>'),
                        const Text('4. Mot de passe: Acheya123'),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Compris'),
            ),
          ],
        );
      },
    );
  }

  /// 🏷️ Obtenir le nom d'affichage du rôle
  String _getRoleDisplayName(String role) {
    switch (role) {
      case 'admin_compagnie':
        return 'Administrateur de Compagnie';
      case 'admin_agence':
        return 'Administrateur d\'Agence';
      case 'agent_agence':
        return 'Agent d\'Agence';
      case 'expert_auto':
        return 'Expert Automobile';
      default:
        return role;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête fixe
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  const Icon(
                    Icons.person_add,
                    color: ModernTheme.primaryColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Créer un utilisateur',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Formulaire scrollable
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Nom complet
                      TextFormField(
                        controller: _nomCompletController,
                        decoration: const InputDecoration(
                          labelText: 'Nom complet *',
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le nom complet est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Email
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email *',
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'L\'email est requis';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'Format d\'email invalide';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Téléphone
                      TextFormField(
                        controller: _telephoneController,
                        decoration: const InputDecoration(
                          labelText: 'Téléphone *',
                          prefixIcon: Icon(Icons.phone),
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le téléphone est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // CIN
                      TextFormField(
                        controller: _cinController,
                        decoration: const InputDecoration(
                          labelText: 'CIN *',
                          prefixIcon: Icon(Icons.badge),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le CIN est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Rôle
                      DropdownButtonFormField<String>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          labelText: 'Rôle *',
                          prefixIcon: Icon(Icons.work),
                        ),
                        items: _roles.map((role) {
                          return DropdownMenuItem(
                            value: role['value'],
                            child: Text(role['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedRole = value;
                            // Reset compagnie et agence si changement de rôle
                            _selectedCompagnieId = null;
                            _selectedAgenceId = null;
                            _agences = [];
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Le rôle est requis';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Compagnie (si rôle nécessite)
                      if (_selectedRole != null && _selectedRole != 'expert_auto')
                        DropdownButtonFormField<String>(
                          value: _selectedCompagnieId,
                          decoration: const InputDecoration(
                            labelText: 'Compagnie *',
                            prefixIcon: Icon(Icons.domain),
                          ),
                          items: _compagnies.map((compagnie) {
                            return DropdownMenuItem<String>(
                              value: compagnie['id'] as String?,
                              child: Text(compagnie['nom'] as String? ?? 'Nom non défini'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCompagnieId = value;
                              _selectedAgenceId = null;
                              _agences = [];
                            });
                            if (value != null) {
                              _loadAgences(value);
                            }
                          },
                          validator: (value) {
                            if (_selectedRole != 'expert_auto' && value == null) {
                              return 'La compagnie est requise';
                            }
                            return null;
                          },
                        ),

                      if (_selectedRole != null && _selectedRole != 'expert_auto')
                        const SizedBox(height: 16),

                      // Agence (si rôle admin_agence ou agent_agence)
                      if (_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence')
                        DropdownButtonFormField<String>(
                          value: _selectedAgenceId,
                          decoration: InputDecoration(
                            labelText: 'Agence *',
                            prefixIcon: const Icon(Icons.business),
                            suffixIcon: _isLoadingAgences
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : null,
                          ),
                          items: _agences.map((agence) {
                            return DropdownMenuItem<String>(
                              value: agence['id'] as String?,
                              child: Text(agence['nom'] as String? ?? 'Nom non défini'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedAgenceId = value;
                            });
                          },
                          validator: (value) {
                            if ((_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence') && value == null) {
                              return 'L\'agence est requise';
                            }
                            return null;
                          },
                        ),

                      if (_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence')
                        const SizedBox(height: 16),

                      // Expérience (optionnel)
                      TextFormField(
                        controller: _experienceController,
                        decoration: const InputDecoration(
                          labelText: 'Années d\'expérience',
                          prefixIcon: Icon(Icons.timeline),
                          helperText: 'Optionnel',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 24), // Espace en bas du formulaire
                    ],
                  ),
                ),
              ),
            ),

            // Boutons fixes en bas
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _createUser,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ModernTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('Créer'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
