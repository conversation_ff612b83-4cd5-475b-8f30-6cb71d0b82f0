import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/theme/modern_theme.dart';
import '../../services/agence_service.dart';
import '../../services/compagnie_service.dart';
import '../../services/admin_creation_service.dart';

/// 🆕 Dialog pour créer un utilisateur directement (Super Admin)
class UserCreateDialog extends StatefulWidget {
  const UserCreateDialog({Key? key}) : super(key: key);

  @override
  State<UserCreateDialog> createState() => _UserCreateDialogState();
}

class _UserCreateDialogState extends State<UserCreateDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomCompletController = TextEditingController();
  final _emailController = TextEditingController();
  final _telephoneController = TextEditingController();
  final _cinController = TextEditingController();
  final _experienceController = TextEditingController();
  final _motDePasseController = TextEditingController();

  String? _selectedRole;
  String? _selectedCompagnieId;
  String? _selectedAgenceId;
  
  List<Map<String, dynamic>> _compagnies = [];
  List<Map<String, dynamic>> _agences = [];
  
  bool _isLoading = false;
  bool _isLoadingAgences = false;

  final List<Map<String, String>> _roles = [
    {'value': 'admin_compagnie', 'label': 'Administrateur de Compagnie'},
    {'value': 'admin_agence', 'label': 'Administrateur d\'Agence'},
    {'value': 'agent_agence', 'label': 'Agent d\'Agence'},
    {'value': 'expert_auto', 'label': 'Expert Automobile'},
  ];

  @override
  void initState() {
    super.initState();
    _loadCompagnies();
  }

  @override
  void dispose() {
    _nomCompletController.dispose();
    _emailController.dispose();
    _telephoneController.dispose();
    _cinController.dispose();
    _experienceController.dispose();
    _motDePasseController.dispose();
    super.dispose();
  }

  /// 🏢 Charger les compagnies
  Future<void> _loadCompagnies() async {
    try {
      final compagnieService = CompagnieService();
      final compagnies = await compagnieService.getCompagniesForDropdown();
      if (mounted) {
        setState(() {
          _compagnies = compagnies;
        });
      }
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur chargement compagnies: $e');
    }
  }

  /// 🏪 Charger les agences selon la compagnie
  Future<void> _loadAgences(String compagnieId) async {
    setState(() {
      _isLoadingAgences = true;
      _selectedAgenceId = null;
      _agences = [];
    });

    try {
      final agenceService = AgenceService();
      final agences = await agenceService.getAgencesForDropdown(compagnieId: compagnieId);
      if (mounted) {
        setState(() {
          _agences = agences;
          _isLoadingAgences = false;
        });
      }
    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur chargement agences: $e');
      if (mounted) {
        setState(() {
          _isLoadingAgences = false;
        });
      }
    }
  }

  /// ✅ Créer l'utilisateur avec Firebase Auth + Firestore
  Future<void> _createUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Séparer le nom complet en nom et prénom
      final nomComplet = _nomCompletController.text.trim();
      final nameParts = nomComplet.split(' ');
      final prenom = nameParts.isNotEmpty ? nameParts.first : '';
      final nom = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      Map<String, dynamic> result;

      // Utiliser le service approprié selon le rôle
      if (_selectedRole == 'admin_compagnie') {
        // Obtenir le nom de la compagnie
        final compagnie = _compagnies.firstWhere(
          (c) => c['id'] == _selectedCompagnieId,
          orElse: () => {'nom': 'Compagnie inconnue'},
        );

        result = await AdminCreationService.createLegitimateAdminCompagnie(
          email: _emailController.text.trim().toLowerCase(),
          password: _motDePasseController.text.trim(),
          nom: nom,
          prenom: prenom,
          compagnieId: _selectedCompagnieId!,
          compagnieNom: compagnie['nom'] as String,
          phone: _telephoneController.text.trim().isNotEmpty
              ? _telephoneController.text.trim()
              : null,
          address: null, // Peut être ajouté plus tard
        );
      } else if (_selectedRole == 'admin_agence') {
        result = await AdminCreationService.createLegitimateAdminAgence(
          email: _emailController.text.trim().toLowerCase(),
          password: _motDePasseController.text.trim(),
          nom: nom,
          prenom: prenom,
          compagnieId: _selectedCompagnieId!,
          agenceId: _selectedAgenceId!,
          phone: _telephoneController.text.trim().isNotEmpty
              ? _telephoneController.text.trim()
              : null,
          address: null,
        );
      } else {
        // Pour les autres rôles, utiliser l'ancienne méthode (à améliorer)
        throw Exception('Création de ${_selectedRole} non encore implémentée avec Firebase Auth');
      }

      if (result['success'] == true) {
        debugPrint('[USER_CREATE] ✅ Utilisateur créé avec succès: ${result['uid']}');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Utilisateur créé avec succès !'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        }
      } else {
        throw Exception(result['error'] ?? 'Erreur inconnue');
      }

    } catch (e) {
      debugPrint('[USER_CREATE] ❌ Erreur création: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la création: $e'),
            backgroundColor: ModernTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête fixe
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  const Icon(
                    Icons.person_add,
                    color: ModernTheme.primaryColor,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Créer un utilisateur',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Formulaire scrollable
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Nom complet
                      TextFormField(
                        controller: _nomCompletController,
                        decoration: const InputDecoration(
                          labelText: 'Nom complet *',
                          prefixIcon: Icon(Icons.person),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le nom complet est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Email
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email *',
                          prefixIcon: Icon(Icons.email),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'L\'email est requis';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return 'Format d\'email invalide';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Mot de passe
                      TextFormField(
                        controller: _motDePasseController,
                        decoration: const InputDecoration(
                          labelText: 'Mot de passe *',
                          prefixIcon: Icon(Icons.lock),
                          helperText: 'Minimum 8 caractères',
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le mot de passe est requis';
                          }
                          if (value.length < 8) {
                            return 'Le mot de passe doit contenir au moins 8 caractères';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Téléphone
                      TextFormField(
                        controller: _telephoneController,
                        decoration: const InputDecoration(
                          labelText: 'Téléphone *',
                          prefixIcon: Icon(Icons.phone),
                        ),
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le téléphone est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // CIN
                      TextFormField(
                        controller: _cinController,
                        decoration: const InputDecoration(
                          labelText: 'CIN *',
                          prefixIcon: Icon(Icons.badge),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le CIN est requis';
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Rôle
                      DropdownButtonFormField<String>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          labelText: 'Rôle *',
                          prefixIcon: Icon(Icons.work),
                        ),
                        items: _roles.map((role) {
                          return DropdownMenuItem(
                            value: role['value'],
                            child: Text(role['label']!),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedRole = value;
                            // Reset compagnie et agence si changement de rôle
                            _selectedCompagnieId = null;
                            _selectedAgenceId = null;
                            _agences = [];
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Le rôle est requis';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // Compagnie (si rôle nécessite)
                      if (_selectedRole != null && _selectedRole != 'expert_auto')
                        DropdownButtonFormField<String>(
                          value: _selectedCompagnieId,
                          decoration: const InputDecoration(
                            labelText: 'Compagnie *',
                            prefixIcon: Icon(Icons.domain),
                          ),
                          items: _compagnies.map((compagnie) {
                            return DropdownMenuItem<String>(
                              value: compagnie['id'] as String?,
                              child: Text(compagnie['nom'] as String? ?? 'Nom non défini'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedCompagnieId = value;
                              _selectedAgenceId = null;
                              _agences = [];
                            });
                            if (value != null) {
                              _loadAgences(value);
                            }
                          },
                          validator: (value) {
                            if (_selectedRole != 'expert_auto' && value == null) {
                              return 'La compagnie est requise';
                            }
                            return null;
                          },
                        ),

                      if (_selectedRole != null && _selectedRole != 'expert_auto')
                        const SizedBox(height: 16),

                      // Agence (si rôle admin_agence ou agent_agence)
                      if (_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence')
                        DropdownButtonFormField<String>(
                          value: _selectedAgenceId,
                          decoration: InputDecoration(
                            labelText: 'Agence *',
                            prefixIcon: const Icon(Icons.business),
                            suffixIcon: _isLoadingAgences
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : null,
                          ),
                          items: _agences.map((agence) {
                            return DropdownMenuItem<String>(
                              value: agence['id'] as String?,
                              child: Text(agence['nom'] as String? ?? 'Nom non défini'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedAgenceId = value;
                            });
                          },
                          validator: (value) {
                            if ((_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence') && value == null) {
                              return 'L\'agence est requise';
                            }
                            return null;
                          },
                        ),

                      if (_selectedRole == 'admin_agence' || _selectedRole == 'agent_agence')
                        const SizedBox(height: 16),

                      // Expérience (optionnel)
                      TextFormField(
                        controller: _experienceController,
                        decoration: const InputDecoration(
                          labelText: 'Années d\'expérience',
                          prefixIcon: Icon(Icons.timeline),
                          helperText: 'Optionnel',
                        ),
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 24), // Espace en bas du formulaire
                    ],
                  ),
                ),
              ),
            ),

            // Boutons fixes en bas
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _createUser,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ModernTheme.primaryColor,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('Créer'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
