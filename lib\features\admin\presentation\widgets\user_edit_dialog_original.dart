import 'package:flutter/material.dart';
import '../../services/user_management_service.dart';

/// ✏️ Dialog d'édition d'utilisateur (Original fonctionnel)
class UserEditDialogOriginal extends StatefulWidget {
  final Map<String, dynamic> user;
  final List<Map<String, dynamic>> compagnies;
  final List<Map<String, dynamic>> agences;
  final VoidCallback onUserUpdated;

  const UserEditDialogOriginal({
    super.key,
    required this.user,
    required this.compagnies,
    required this.agences,
    required this.onUserUpdated,
  });

  @override
  State<UserEditDialogOriginal> createState() => _UserEditDialogOriginalState();
}

class _UserEditDialogOriginalState extends State<UserEditDialogOriginal> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nomController;
  late TextEditingController _prenomController;
  late TextEditingController _phoneController;
  late TextEditingController _addressController;

  String? _selectedRole;
  String? _selectedCompagnie;
  String? _selectedAgence;
  String? _selectedStatus;
  bool _isLoading = false;

  final List<Map<String, String>> _roles = [
    {'value': 'super_admin', 'label': 'Super Admin'},
    {'value': 'admin_compagnie', 'label': 'Administrateur de Compagnie'},
    {'value': 'admin_agence', 'label': 'Administrateur d\'Agence'},
    {'value': 'agent_agence', 'label': 'Agent d\'Agence'},
    {'value': 'expert_auto', 'label': 'Expert Automobile'},
    {'value': 'conducteur', 'label': 'Conducteur'},
  ];

  final List<Map<String, String>> _statuses = [
    {'value': 'actif', 'label': 'Actif'},
    {'value': 'en_attente', 'label': 'En attente'},
    {'value': 'desactive', 'label': 'Désactivé'},
    {'value': 'supprime', 'label': 'Supprimé'},
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  /// 🔧 Initialiser les contrôleurs avec les données existantes
  void _initializeControllers() {
    _nomController = TextEditingController(text: widget.user['nom'] ?? '');
    _prenomController = TextEditingController(text: widget.user['prenom'] ?? '');
    _phoneController = TextEditingController(text: widget.user['phone'] ?? '');
    _addressController = TextEditingController(text: widget.user['address'] ?? '');
    
    _selectedRole = widget.user['role'];
    _selectedCompagnie = widget.user['compagnieId'];
    _selectedAgence = widget.user['agenceId'];
    _selectedStatus = widget.user['status'] ?? 'actif';
  }

  @override
  void dispose() {
    _nomController.dispose();
    _prenomController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  /// 💾 Sauvegarder les modifications
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final updates = <String, dynamic>{};

      // Vérifier les changements
      if (_nomController.text.trim() != (widget.user['nom'] ?? '')) {
        updates['nom'] = _nomController.text.trim();
      }
      
      if (_prenomController.text.trim() != (widget.user['prenom'] ?? '')) {
        updates['prenom'] = _prenomController.text.trim();
      }
      
      if (_phoneController.text.trim() != (widget.user['phone'] ?? '')) {
        updates['phone'] = _phoneController.text.trim().isNotEmpty 
            ? _phoneController.text.trim() 
            : null;
      }
      
      if (_addressController.text.trim() != (widget.user['address'] ?? '')) {
        updates['address'] = _addressController.text.trim().isNotEmpty 
            ? _addressController.text.trim() 
            : null;
      }
      
      if (_selectedRole != widget.user['role']) {
        updates['role'] = _selectedRole;
      }
      
      if (_selectedCompagnie != widget.user['compagnieId']) {
        updates['compagnieId'] = _selectedCompagnie;
      }
      
      if (_selectedAgence != widget.user['agenceId']) {
        updates['agenceId'] = _selectedAgence;
      }
      
      if (_selectedStatus != widget.user['status']) {
        updates['status'] = _selectedStatus;
      }

      if (updates.isNotEmpty) {
        final result = await UserManagementService.updateUser(
          userId: widget.user['id'],
          updates: updates,
        );

        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Utilisateur mis à jour avec succès !'),
              backgroundColor: Colors.green,
            ),
          );
          widget.onUserUpdated();
        } else {
          throw Exception(result['error'] ?? 'Erreur inconnue');
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aucune modification détectée'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.of(context).pop();
      }

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 🔍 Obtenir les agences filtrées par compagnie
  List<Map<String, dynamic>> _getFilteredAgences() {
    if (_selectedCompagnie == null) return [];
    return widget.agences
        .where((agence) => agence['compagnieId'] == _selectedCompagnie)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Titre
                Row(
                  children: [
                    const Icon(Icons.edit, color: Colors.blue),
                    const SizedBox(width: 8),
                    const Text(
                      'Modifier l\'utilisateur',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                Text(
                  widget.user['email'] ?? 'Email non disponible',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Informations personnelles
                const Text(
                  'Informations Personnelles',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _prenomController,
                        decoration: const InputDecoration(
                          labelText: 'Prénom',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Prénom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _nomController,
                        decoration: const InputDecoration(
                          labelText: 'Nom',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Nom requis';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'Téléphone',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.phone,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _addressController,
                        decoration: const InputDecoration(
                          labelText: 'Adresse',
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Rôle et statut
                const Text(
                  'Rôle et Statut',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Rôle',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedRole,
                        items: _roles.map((role) => DropdownMenuItem(
                          value: role['value'],
                          child: Text(role['label']!),
                        )).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedRole = value;
                            // Reset compagnie et agence si le rôle change
                            if (value == 'conducteur') {
                              _selectedCompagnie = null;
                              _selectedAgence = null;
                            }
                          });
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Statut',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedStatus,
                        items: _statuses.map((status) => DropdownMenuItem(
                          value: status['value'],
                          child: Text(status['label']!),
                        )).toList(),
                        onChanged: (value) {
                          setState(() => _selectedStatus = value);
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Boutons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _saveChanges,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Sauvegarder'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
