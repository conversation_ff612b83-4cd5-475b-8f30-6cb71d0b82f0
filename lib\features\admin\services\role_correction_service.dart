import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🔧 Service pour corriger temporairement les rôles utilisateur
class RoleCorrectionService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔧 Corriger le rôle de l'utilisateur actuel en Super Admin (TEMPORAIRE)
  static Future<Map<String, dynamic>> promoteCurrentUserToSuperAdmin() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'success': false,
          'error': 'Aucun utilisateur connecté',
        };
      }

      debugPrint('[ROLE_CORRECTION] 🔧 Promotion utilisateur actuel en Super Admin...');
      debugPrint('[ROLE_CORRECTION]   - UID: ${currentUser.uid}');
      debugPrint('[ROLE_CORRECTION]   - Email: ${currentUser.email}');

      // Vérifier si le document existe
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        // Créer le document s'il n'existe pas
        await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .set({
          'email': currentUser.email,
          'role': 'super_admin',
          'status': 'actif',
          'nom': 'Super',
          'prenom': 'Admin',
          'phone': '+216 70 000 000',
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
          'created_by': 'system_correction',
          'isLegitimate': true,
          'accessLevel': 'production',
          'originalRole': 'admin_compagnie', // Garder trace du rôle original
          'promotedAt': FieldValue.serverTimestamp(),
          'promotionReason': 'Correction temporaire pour création d\'utilisateurs',
        });

        debugPrint('[ROLE_CORRECTION] ✅ Document créé avec rôle super_admin');
      } else {
        // Mettre à jour le rôle existant
        final currentData = userDoc.data()!;
        
        await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .update({
          'role': 'super_admin',
          'updated_at': FieldValue.serverTimestamp(),
          'originalRole': currentData['role'], // Sauvegarder le rôle original
          'promotedAt': FieldValue.serverTimestamp(),
          'promotionReason': 'Correction temporaire pour création d\'utilisateurs',
        });

        debugPrint('[ROLE_CORRECTION] ✅ Rôle mis à jour: ${currentData['role']} → super_admin');
      }

      // Attendre que Firestore se synchronise
      await Future.delayed(const Duration(milliseconds: 1000));

      // Vérifier que la modification a bien été appliquée
      final updatedDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (updatedDoc.exists && updatedDoc.data()!['role'] == 'super_admin') {
        return {
          'success': true,
          'uid': currentUser.uid,
          'email': currentUser.email,
          'originalRole': updatedDoc.data()!['originalRole'],
          'newRole': 'super_admin',
        };
      } else {
        return {
          'success': false,
          'error': 'La modification du rôle n\'a pas été appliquée',
        };
      }

    } catch (e) {
      debugPrint('[ROLE_CORRECTION] ❌ Erreur promotion: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
      };
    }
  }

  /// 🔄 Restaurer le rôle original de l'utilisateur
  static Future<Map<String, dynamic>> restoreOriginalRole() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'success': false,
          'error': 'Aucun utilisateur connecté',
        };
      }

      debugPrint('[ROLE_CORRECTION] 🔄 Restauration rôle original...');

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        return {
          'success': false,
          'error': 'Document utilisateur non trouvé',
        };
      }

      final userData = userDoc.data()!;
      final originalRole = userData['originalRole'] as String?;

      if (originalRole == null) {
        return {
          'success': false,
          'error': 'Aucun rôle original trouvé',
        };
      }

      // Restaurer le rôle original
      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .update({
        'role': originalRole,
        'updated_at': FieldValue.serverTimestamp(),
        'restoredAt': FieldValue.serverTimestamp(),
        'restorationReason': 'Restauration après correction temporaire',
      });

      debugPrint('[ROLE_CORRECTION] ✅ Rôle restauré: super_admin → $originalRole');

      return {
        'success': true,
        'uid': currentUser.uid,
        'email': currentUser.email,
        'restoredRole': originalRole,
      };

    } catch (e) {
      debugPrint('[ROLE_CORRECTION] ❌ Erreur restauration: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
      };
    }
  }

  /// 🔍 Vérifier si l'utilisateur actuel a été promu temporairement
  static Future<bool> isTemporarilyPromoted() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      return userData['role'] == 'super_admin' && 
             userData['originalRole'] != null &&
             userData['promotedAt'] != null;

    } catch (e) {
      debugPrint('[ROLE_CORRECTION] ❌ Erreur vérification: $e');
      return false;
    }
  }

  /// 📊 Obtenir les informations de promotion
  static Future<Map<String, dynamic>?> getPromotionInfo() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return null;

      final userData = userDoc.data()!;
      
      if (userData['originalRole'] == null) return null;

      return {
        'currentRole': userData['role'],
        'originalRole': userData['originalRole'],
        'promotedAt': userData['promotedAt'],
        'promotionReason': userData['promotionReason'],
        'isTemporarilyPromoted': userData['role'] == 'super_admin' && userData['originalRole'] != null,
      };

    } catch (e) {
      debugPrint('[ROLE_CORRECTION] ❌ Erreur info promotion: $e');
      return null;
    }
  }

  /// 🧹 Nettoyer les données de promotion (supprimer les champs temporaires)
  static Future<bool> cleanupPromotionData() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .update({
        'originalRole': FieldValue.delete(),
        'promotedAt': FieldValue.delete(),
        'promotionReason': FieldValue.delete(),
        'restoredAt': FieldValue.delete(),
        'restorationReason': FieldValue.delete(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      debugPrint('[ROLE_CORRECTION] 🧹 Données de promotion nettoyées');
      return true;

    } catch (e) {
      debugPrint('[ROLE_CORRECTION] ❌ Erreur nettoyage: $e');
      return false;
    }
  }
}
