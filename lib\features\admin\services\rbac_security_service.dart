import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🔐 Service de sécurité RBAC (Role-Based Access Control) STRICT
/// 
/// RÈGLES DE SÉCURITÉ PRODUCTION :
/// - Aucune élévation temporaire de rôle
/// - Aucun contournement de permissions
/// - Vérifications strictes à chaque opération
/// - Audit de toutes les tentatives d'accès
class RBACSecurityService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔐 Rôles autorisés dans le système
  static const String ROLE_SUPER_ADMIN = 'super_admin';
  static const String ROLE_ADMIN_COMPAGNIE = 'admin_compagnie';
  static const String ROLE_ADMIN_AGENCE = 'admin_agence';
  static const String ROLE_AGENT = 'agent_agence';
  static const String ROLE_CONDUCTEUR = 'conducteur';
  static const String ROLE_EXPERT = 'expert_auto';

  /// 🔐 Permissions par rôle (STRICT)
  static const Map<String, List<String>> ROLE_PERMISSIONS = {
    ROLE_SUPER_ADMIN: [
      'create_admin_compagnie',
      'create_admin_agence',
      'create_agent',
      'create_expert',
      'manage_all_users',
      'manage_all_companies',
      'view_all_data',
      'system_administration',
    ],
    ROLE_ADMIN_COMPAGNIE: [
      'manage_own_company_data',
      'view_own_company_agencies',
      'view_own_company_agents',
      'view_own_company_claims',
    ],
    ROLE_ADMIN_AGENCE: [
      'create_agent',
      'manage_own_agency_data',
      'view_own_agency_agents',
      'view_own_agency_claims',
    ],
    ROLE_AGENT: [
      'create_claim',
      'view_own_claims',
      'manage_own_profile',
    ],
    ROLE_CONDUCTEUR: [
      'create_claim',
      'view_own_claims',
      'manage_own_profile',
    ],
    ROLE_EXPERT: [
      'evaluate_claims',
      'create_expertise_reports',
      'view_assigned_claims',
    ],
  };

  /// 🔍 Vérifier si l'utilisateur actuel a une permission spécifique
  static Future<SecurityCheckResult> hasPermission(String permission) async {
    try {
      debugPrint('[RBAC_SECURITY] 🔍 Vérification permission: $permission');

      // 1. Vérifier l'authentification
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return SecurityCheckResult.denied(
          reason: 'Utilisateur non authentifié',
          action: 'Se connecter',
        );
      }

      // 2. Récupérer les données utilisateur
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        await _logSecurityEvent(
          action: 'permission_check_failed',
          reason: 'Document utilisateur non trouvé',
          permission: permission,
        );
        
        return SecurityCheckResult.denied(
          reason: 'Données utilisateur non trouvées',
          action: 'Contacter l\'administrateur',
        );
      }

      final userData = Map<String, dynamic>.from(userDoc.data()!);
      final userRole = userData['role'] as String?;

      if (userRole == null) {
        await _logSecurityEvent(
          action: 'permission_check_failed',
          reason: 'Rôle utilisateur non défini',
          permission: permission,
        );

        return SecurityCheckResult.denied(
          reason: 'Rôle utilisateur non défini',
          action: 'Contacter l\'administrateur',
        );
      }

      // 3. Vérifier les permissions du rôle
      final rolePermissions = ROLE_PERMISSIONS[userRole] ?? [];
      final hasPermission = rolePermissions.contains(permission);

      // 4. Logger la tentative d'accès
      await _logSecurityEvent(
        action: hasPermission ? 'permission_granted' : 'permission_denied',
        reason: hasPermission ? 'Permission accordée' : 'Permission insuffisante',
        permission: permission,
        userRole: userRole,
      );

      if (hasPermission) {
        debugPrint('[RBAC_SECURITY] ✅ Permission accordée: $permission pour $userRole');
        return SecurityCheckResult.granted(
          userRole: userRole,
          userId: currentUser.uid,
          userEmail: currentUser.email ?? '',
        );
      } else {
        debugPrint('[RBAC_SECURITY] ❌ Permission refusée: $permission pour $userRole');
        return SecurityCheckResult.denied(
          reason: 'Rôle $userRole ne dispose pas de la permission $permission',
          action: 'Contacter un administrateur avec des permissions suffisantes',
          currentRole: userRole,
        );
      }

    } catch (e) {
      debugPrint('[RBAC_SECURITY] ❌ Erreur vérification permission: $e');
      
      await _logSecurityEvent(
        action: 'permission_check_error',
        reason: 'Erreur système: $e',
        permission: permission,
      );

      return SecurityCheckResult.error(
        error: 'Erreur système lors de la vérification des permissions',
      );
    }
  }

  /// 🔐 Vérifier si l'utilisateur peut créer des comptes admin_compagnie
  static Future<SecurityCheckResult> canCreateAdminCompagnie() async {
    return await hasPermission('create_admin_compagnie');
  }

  /// 🔐 Vérifier si l'utilisateur peut créer des comptes admin_agence
  static Future<SecurityCheckResult> canCreateAdminAgence() async {
    return await hasPermission('create_admin_agence');
  }

  /// 🔐 Vérifier si l'utilisateur est Super Admin
  static Future<bool> isSuperAdmin() async {
    final result = await hasPermission('system_administration');
    return result.isGranted;
  }

  /// 📝 Logger un événement de sécurité
  static Future<void> _logSecurityEvent({
    required String action,
    required String reason,
    String? permission,
    String? userRole,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      
      await _firestore.collection('security_audit_logs').add({
        'action': action,
        'reason': reason,
        'permission': permission,
        'userRole': userRole,
        'userId': currentUser?.uid,
        'userEmail': currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
        'ipAddress': 'N/A', // TODO: Récupérer l'IP si nécessaire
        'userAgent': 'Flutter App',
      });
    } catch (e) {
      debugPrint('[RBAC_SECURITY] ❌ Erreur log sécurité: $e');
    }
  }

  /// 📊 Obtenir les permissions d'un rôle
  static List<String> getPermissionsForRole(String role) {
    return ROLE_PERMISSIONS[role] ?? [];
  }

  /// 📊 Obtenir tous les rôles disponibles
  static List<String> getAllRoles() {
    return ROLE_PERMISSIONS.keys.toList();
  }

  /// 🔍 Obtenir les informations de sécurité de l'utilisateur actuel
  static Future<Map<String, dynamic>?> getCurrentUserSecurityInfo() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return null;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return null;

      final userData = Map<String, dynamic>.from(userDoc.data()!);
      final userRole = userData['role'] as String?;

      return {
        'uid': currentUser.uid,
        'email': currentUser.email,
        'role': userRole,
        'permissions': getPermissionsForRole(userRole ?? ''),
        'isSuperAdmin': userRole == ROLE_SUPER_ADMIN,
        'lastSignIn': currentUser.metadata.lastSignInTime?.toIso8601String(),
      };
    } catch (e) {
      debugPrint('[RBAC_SECURITY] ❌ Erreur info sécurité: $e');
      return null;
    }
  }
}

/// 📋 Résultat d'une vérification de sécurité
class SecurityCheckResult {
  final bool isGranted;
  final bool isError;
  final String? reason;
  final String? action;
  final String? currentRole;
  final String? userRole;
  final String? userId;
  final String? userEmail;
  final String? error;

  SecurityCheckResult._({
    required this.isGranted,
    required this.isError,
    this.reason,
    this.action,
    this.currentRole,
    this.userRole,
    this.userId,
    this.userEmail,
    this.error,
  });

  factory SecurityCheckResult.granted({
    required String userRole,
    required String userId,
    required String userEmail,
  }) {
    return SecurityCheckResult._(
      isGranted: true,
      isError: false,
      userRole: userRole,
      userId: userId,
      userEmail: userEmail,
    );
  }

  factory SecurityCheckResult.denied({
    required String reason,
    required String action,
    String? currentRole,
  }) {
    return SecurityCheckResult._(
      isGranted: false,
      isError: false,
      reason: reason,
      action: action,
      currentRole: currentRole,
    );
  }

  factory SecurityCheckResult.error({
    required String error,
  }) {
    return SecurityCheckResult._(
      isGranted: false,
      isError: true,
      error: error,
    );
  }
}
