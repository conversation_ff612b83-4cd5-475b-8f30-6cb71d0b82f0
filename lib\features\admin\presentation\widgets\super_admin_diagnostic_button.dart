import 'package:flutter/material.dart';
import '../../services/super_admin_diagnostic_service.dart';

/// 🔍 Bouton de diagnostic pour le Super Admin
class SuperAdminDiagnosticButton extends StatefulWidget {
  const SuperAdminDiagnosticButton({super.key});

  @override
  State<SuperAdminDiagnosticButton> createState() => _SuperAdminDiagnosticButtonState();
}

class _SuperAdminDiagnosticButtonState extends State<SuperAdminDiagnosticButton> {
  bool _isRunning = false;
  Map<String, dynamic>? _lastDiagnostic;

  /// 🔍 Exécuter le diagnostic
  Future<void> _runDiagnostic() async {
    setState(() => _isRunning = true);

    try {
      final diagnostic = await SuperAdminDiagnosticService.fullDiagnostic();
      setState(() => _lastDiagnostic = diagnostic);
      
      if (mounted) {
        _showDiagnosticDialog(diagnostic);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isRunning = false);
    }
  }

  /// 🔧 Corriger automatiquement les problèmes
  Future<void> _autoFix() async {
    setState(() => _isRunning = true);

    try {
      final created = await SuperAdminDiagnosticService.createSuperAdminFirestoreDocument();
      
      if (created) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Document Super Admin créé avec succès !'),
              backgroundColor: Colors.green,
            ),
          );
        }
        
        // Re-exécuter le diagnostic
        await _runDiagnostic();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ Échec de la correction automatique'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur correction: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isRunning = false);
    }
  }

  /// 📊 Afficher le dialog de diagnostic
  void _showDiagnosticDialog(Map<String, dynamic> diagnostic) {
    final auth = diagnostic['firebase_auth'] as Map<String, dynamic>? ?? {};
    final firestore = diagnostic['firestore_user'] as Map<String, dynamic>? ?? {};
    final permissions = diagnostic['permissions'] as Map<String, dynamic>? ?? {};
    final recommendations = diagnostic['recommendations'] as List<dynamic>? ?? [];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.medical_services, color: Colors.blue),
            SizedBox(width: 8),
            Text('🔍 Diagnostic Super Admin'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Firebase Auth
                _buildSection(
                  '🔐 Firebase Auth',
                  [
                    'Connecté: ${auth['is_authenticated'] == true ? '✅' : '❌'}',
                    'Email: ${auth['user_email'] ?? 'N/A'}',
                    'Email correct: ${auth['email_matches'] == true ? '✅' : '❌'}',
                    'UID: ${auth['user_uid'] ?? 'N/A'}',
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Firestore
                _buildSection(
                  '📄 Firestore',
                  [
                    'Document existe: ${firestore['document_exists'] == true ? '✅' : '❌'}',
                    if (firestore['document_exists'] == true) ...[
                      'Rôle: ${firestore['role'] ?? 'N/A'}',
                      'Super Admin: ${firestore['is_super_admin_role'] == true ? '✅' : '❌'}',
                      'Status: ${firestore['status'] ?? 'N/A'}',
                    ],
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Permissions
                _buildSection(
                  '🔑 Permissions',
                  [
                    'Créer Admin Compagnie: ${permissions['can_create_admin_compagnie'] == true ? '✅' : '❌'}',
                    'Créer Admin Agence: ${permissions['can_create_admin_agence'] == true ? '✅' : '❌'}',
                    'Dashboard Super Admin: ${permissions['can_access_super_admin_dashboard'] == true ? '✅' : '❌'}',
                  ],
                ),
                
                // Recommandations
                if (recommendations.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildSection(
                    '💡 Recommandations',
                    recommendations.map((r) => r.toString()).toList(),
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: [
          if (firestore['document_exists'] != true)
            TextButton.icon(
              onPressed: _isRunning ? null : _autoFix,
              icon: const Icon(Icons.build),
              label: const Text('🔧 Corriger'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 📋 Construire une section du diagnostic
  Widget _buildSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 4),
          child: Text(
            '• $item',
            style: const TextStyle(fontSize: 14),
          ),
        )),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton diagnostic
        ElevatedButton.icon(
          onPressed: _isRunning ? null : _runDiagnostic,
          icon: _isRunning 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.medical_services),
          label: const Text('🔍 Diagnostic'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        
        // Indicateur de statut
        if (_lastDiagnostic != null) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getStatusText(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 🎨 Obtenir la couleur du statut
  Color _getStatusColor() {
    if (_lastDiagnostic == null) return Colors.grey;
    
    final permissions = _lastDiagnostic!['permissions'] as Map<String, dynamic>? ?? {};
    final canCreate = permissions['can_create_admin_compagnie'] == true;
    
    return canCreate ? Colors.green : Colors.red;
  }

  /// 📝 Obtenir le texte du statut
  String _getStatusText() {
    if (_lastDiagnostic == null) return 'Non testé';
    
    final permissions = _lastDiagnostic!['permissions'] as Map<String, dynamic>? ?? {};
    final canCreate = permissions['can_create_admin_compagnie'] == true;
    
    return canCreate ? 'OK' : 'Problème';
  }
}
