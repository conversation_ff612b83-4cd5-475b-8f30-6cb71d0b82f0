import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/config/app_router.dart';
import 'features/admin/services/simple_super_admin.dart';
import 'features/admin_compagnie/services/test_admin_compagnie.dart';
import 'features/admin_compagnie/services/auth_diagnostic_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('[CONSTAT_APP] Firebase initialized successfully');

    // 🚀 TEMPORAIRE: Désactiver les services pour tester le splash screen
    // _initializeBackgroundServices();

  } catch (e) {
    debugPrint('[CONSTAT_APP] Firebase initialization failed: $e');
  }

  runApp(const ProviderScope(child: MyApp()));
}

/// 🔧 Initialiser les services en arrière-plan pour éviter de bloquer le splash screen
void _initializeBackgroundServices() {
  // Exécuter en arrière-plan sans bloquer l'UI
  Future.delayed(const Duration(milliseconds: 500), () async {
    try {
      debugPrint('[CONSTAT_APP] 🚀 Début initialisation services arrière-plan...');

      // Créer le Super Admin simplifié
      await SimpleSuperAdmin.createSuperAdmin();
      debugPrint('[CONSTAT_APP] ✅ Super Admin initialisé');

      // Créer l'Admin Compagnie de test
      await TestAdminCompagnie.createTestAdminCompagnie();
      debugPrint('[CONSTAT_APP] ✅ Admin Compagnie de test initialisé');

      // Diagnostic de l'authentification Admin Compagnie
      await AuthDiagnosticService.testAdminCompagnieLogin();
      debugPrint('[CONSTAT_APP] ✅ Diagnostic Auth Admin Compagnie terminé');

      debugPrint('[CONSTAT_APP] 🎉 Tous les services arrière-plan initialisés');
    } catch (e) {
      debugPrint('[CONSTAT_APP] ⚠️ Erreur services arrière-plan: $e');
    }
  });
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Constat Tunisie',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      onGenerateRoute: AppRouter.generateRoute,
      initialRoute: AppRouter.splash,
      debugShowCheckedModeBanner: false,
    );
  }
}
