import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// 🔧 Service pour corriger le problème du Super Admin
class SuperAdminFixService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 🔧 Corriger le document Super Admin dans Firestore
  static Future<Map<String, dynamic>> fixSuperAdminDocument() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return {
          'success': false,
          'error': 'Aucun utilisateur connecté',
        };
      }

      debugPrint('[SUPER_ADMIN_FIX] 🔧 Correction document Super Admin...');
      debugPrint('[SUPER_ADMIN_FIX] 👤 UID: ${currentUser.uid}');
      debugPrint('[SUPER_ADMIN_FIX] 📧 Email: ${currentUser.email}');

      // Vérifier si le document existe déjà
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (userDoc.exists) {
        debugPrint('[SUPER_ADMIN_FIX] 📄 Document existant trouvé');
        final userData = userDoc.data()!;
        debugPrint('[SUPER_ADMIN_FIX] 🏷️ Rôle actuel: ${userData['role']}');
        
        // Mettre à jour le rôle si nécessaire
        if (userData['role'] != 'super_admin') {
          await _firestore
              .collection('users')
              .doc(currentUser.uid)
              .update({
            'role': 'super_admin',
            'updated_at': FieldValue.serverTimestamp(),
            'fixed_at': FieldValue.serverTimestamp(),
            'fixed_reason': 'Correction automatique du rôle Super Admin',
          });
          debugPrint('[SUPER_ADMIN_FIX] ✅ Rôle mis à jour vers super_admin');
        }
      } else {
        debugPrint('[SUPER_ADMIN_FIX] 📄 Aucun document trouvé, création...');
        
        // Créer le document Super Admin
        await _firestore
            .collection('users')
            .doc(currentUser.uid)
            .set({
          'uid': currentUser.uid,
          'email': currentUser.email,
          'role': 'super_admin',
          'status': 'actif',
          'isActive': true,
          'nom': 'Super',
          'prenom': 'Admin',
          'displayName': 'Super Admin',
          'phone': '+216 70 000 000',
          'created_at': FieldValue.serverTimestamp(),
          'updated_at': FieldValue.serverTimestamp(),
          'created_by': 'system_fix',
          'origin': 'system_creation',
          'isLegitimate': true,
          'accessLevel': 'production',
          'permissions': [
            'create_admin_compagnie',
            'create_admin_agence',
            'create_agent',
            'create_expert',
            'manage_all_users',
            'manage_all_companies',
            'view_all_data',
            'system_administration',
          ],
        });
        debugPrint('[SUPER_ADMIN_FIX] ✅ Document Super Admin créé');
      }

      // Vérifier que la correction a fonctionné
      await Future.delayed(const Duration(milliseconds: 1000));
      
      final verificationDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (verificationDoc.exists && verificationDoc.data()!['role'] == 'super_admin') {
        debugPrint('[SUPER_ADMIN_FIX] ✅ Vérification réussie - Super Admin configuré');
        return {
          'success': true,
          'uid': currentUser.uid,
          'email': currentUser.email,
          'role': 'super_admin',
          'message': 'Document Super Admin corrigé avec succès',
        };
      } else {
        return {
          'success': false,
          'error': 'La vérification a échoué après la correction',
        };
      }

    } catch (e) {
      debugPrint('[SUPER_ADMIN_FIX] ❌ Erreur correction: $e');
      return {
        'success': false,
        'error': 'Erreur lors de la correction: $e',
      };
    }
  }

  /// 🔍 Diagnostiquer le problème Super Admin
  static Future<Map<String, dynamic>> diagnoseSuperAdminIssue() async {
    try {
      final currentUser = _auth.currentUser;
      
      final diagnosis = <String, dynamic>{
        'timestamp': DateTime.now().toIso8601String(),
        'firebase_auth': {},
        'firestore_document': {},
        'issues': <String>[],
        'recommendations': <String>[],
      };

      // 1. Vérifier Firebase Auth
      if (currentUser == null) {
        diagnosis['firebase_auth'] = {
          'connected': false,
          'issue': 'Aucun utilisateur connecté',
        };
        diagnosis['issues'].add('Utilisateur non connecté à Firebase Auth');
        diagnosis['recommendations'].add('Se <NAME_EMAIL>');
        return diagnosis;
      }

      diagnosis['firebase_auth'] = {
        'connected': true,
        'uid': currentUser.uid,
        'email': currentUser.email,
        'email_verified': currentUser.emailVerified,
        'is_correct_email': currentUser.email == '<EMAIL>',
      };

      if (currentUser.email != '<EMAIL>') {
        diagnosis['issues'].add('Email incorrect: ${currentUser.email}');
        diagnosis['recommendations'].add('Se <NAME_EMAIL>');
      }

      // 2. Vérifier le document Firestore
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        diagnosis['firestore_document'] = {
          'exists': false,
          'issue': 'Document Firestore manquant',
        };
        diagnosis['issues'].add('Document Firestore manquant pour UID: ${currentUser.uid}');
        diagnosis['recommendations'].add('Exécuter fixSuperAdminDocument() pour créer le document');
      } else {
        final userData = userDoc.data()!;
        diagnosis['firestore_document'] = {
          'exists': true,
          'role': userData['role'],
          'status': userData['status'],
          'email': userData['email'],
          'is_super_admin': userData['role'] == 'super_admin',
          'data': userData,
        };

        if (userData['role'] != 'super_admin') {
          diagnosis['issues'].add('Rôle incorrect: ${userData['role']} (attendu: super_admin)');
          diagnosis['recommendations'].add('Exécuter fixSuperAdminDocument() pour corriger le rôle');
        }

        if (userData['status'] != 'actif') {
          diagnosis['issues'].add('Statut incorrect: ${userData['status']} (attendu: actif)');
          diagnosis['recommendations'].add('Corriger le statut en "actif"');
        }
      }

      // 3. Résumé
      diagnosis['has_issues'] = (diagnosis['issues'] as List).isNotEmpty;
      diagnosis['can_create_users'] = (diagnosis['issues'] as List).isEmpty;

      return diagnosis;

    } catch (e) {
      debugPrint('[SUPER_ADMIN_FIX] ❌ Erreur diagnostic: $e');
      return {
        'error': 'Erreur lors du diagnostic: $e',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 🔍 Test rapide des permissions
  static Future<bool> testSuperAdminPermissions() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return false;

      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      return userData['role'] == 'super_admin' && userData['status'] == 'actif';

    } catch (e) {
      debugPrint('[SUPER_ADMIN_FIX] ❌ Erreur test permissions: $e');
      return false;
    }
  }

  /// 📊 Afficher le diagnostic complet
  static Future<void> printFullDiagnosis() async {
    debugPrint('[SUPER_ADMIN_FIX] 🔍 === DIAGNOSTIC COMPLET ===');
    
    final diagnosis = await diagnoseSuperAdminIssue();
    
    debugPrint('[SUPER_ADMIN_FIX] 📅 Timestamp: ${diagnosis['timestamp']}');
    
    // Firebase Auth
    final auth = diagnosis['firebase_auth'] as Map<String, dynamic>? ?? {};
    debugPrint('[SUPER_ADMIN_FIX] 🔐 Firebase Auth:');
    debugPrint('[SUPER_ADMIN_FIX]   - Connecté: ${auth['connected']}');
    debugPrint('[SUPER_ADMIN_FIX]   - UID: ${auth['uid']}');
    debugPrint('[SUPER_ADMIN_FIX]   - Email: ${auth['email']}');
    debugPrint('[SUPER_ADMIN_FIX]   - Email correct: ${auth['is_correct_email']}');
    
    // Firestore
    final firestore = diagnosis['firestore_document'] as Map<String, dynamic>? ?? {};
    debugPrint('[SUPER_ADMIN_FIX] 📄 Firestore:');
    debugPrint('[SUPER_ADMIN_FIX]   - Document existe: ${firestore['exists']}');
    if (firestore['exists'] == true) {
      debugPrint('[SUPER_ADMIN_FIX]   - Rôle: ${firestore['role']}');
      debugPrint('[SUPER_ADMIN_FIX]   - Statut: ${firestore['status']}');
      debugPrint('[SUPER_ADMIN_FIX]   - Est Super Admin: ${firestore['is_super_admin']}');
    }
    
    // Problèmes
    final issues = diagnosis['issues'] as List<dynamic>? ?? [];
    if (issues.isNotEmpty) {
      debugPrint('[SUPER_ADMIN_FIX] ❌ Problèmes détectés:');
      for (final issue in issues) {
        debugPrint('[SUPER_ADMIN_FIX]   - $issue');
      }
    }
    
    // Recommandations
    final recommendations = diagnosis['recommendations'] as List<dynamic>? ?? [];
    if (recommendations.isNotEmpty) {
      debugPrint('[SUPER_ADMIN_FIX] 💡 Recommandations:');
      for (final rec in recommendations) {
        debugPrint('[SUPER_ADMIN_FIX]   - $rec');
      }
    }
    
    debugPrint('[SUPER_ADMIN_FIX] 🎯 Peut créer des utilisateurs: ${diagnosis['can_create_users']}');
    debugPrint('[SUPER_ADMIN_FIX] 🔍 === FIN DIAGNOSTIC ===');
  }
}
