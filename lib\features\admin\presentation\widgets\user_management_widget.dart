import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/user_management_service.dart';
import '../../services/compagnie_service.dart';
import '../../services/agence_service.dart';
import 'user_create_form_widget.dart';
import 'user_edit_dialog.dart';

/// 👥 Widget de gestion des utilisateurs (Original fonctionnel)
class UserManagementWidget extends StatefulWidget {
  const UserManagementWidget({super.key});

  @override
  State<UserManagementWidget> createState() => _UserManagementWidgetState();
}

class _UserManagementWidgetState extends State<UserManagementWidget> {
  List<Map<String, dynamic>> _users = [];
  List<Map<String, dynamic>> _compagnies = [];
  List<Map<String, dynamic>> _agences = [];
  bool _isLoading = false;
  
  // Filtres
  String? _selectedRole;
  String? _selectedCompagnie;
  String? _selectedAgence;
  String? _selectedStatus;
  String _searchQuery = '';

  final List<String> _roles = [
    'super_admin',
    'admin_compagnie',
    'admin_agence',
    'agent_agence',
    'expert_auto',
    'conducteur',
  ];

  final List<String> _statuses = [
    'actif',
    'en_attente',
    'desactive',
    'supprime',
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 📥 Charger toutes les données
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      await Future.wait([
        _loadUsers(),
        _loadCompagnies(),
        _loadAgences(),
      ]);
    } catch (e) {
      debugPrint('[USER_MANAGEMENT_WIDGET] ❌ Erreur chargement: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 👥 Charger les utilisateurs
  Future<void> _loadUsers() async {
    try {
      final result = await UserManagementService.getAllUsers(
        roleFilter: _selectedRole,
        compagnieFilter: _selectedCompagnie,
        agenceFilter: _selectedAgence,
        statusFilter: _selectedStatus,
      );
      
      setState(() {
        _users = result['users'] ?? [];
      });
    } catch (e) {
      debugPrint('[USER_MANAGEMENT_WIDGET] ❌ Erreur chargement utilisateurs: $e');
    }
  }

  /// 🏢 Charger les compagnies
  Future<void> _loadCompagnies() async {
    try {
      final compagnies = await CompagnieService.getAllCompagnies();
      setState(() => _compagnies = compagnies);
    } catch (e) {
      debugPrint('[USER_MANAGEMENT_WIDGET] ❌ Erreur chargement compagnies: $e');
    }
  }

  /// 🏪 Charger les agences
  Future<void> _loadAgences() async {
    try {
      final agences = await AgenceService.getAllAgences();
      setState(() => _agences = agences);
    } catch (e) {
      debugPrint('[USER_MANAGEMENT_WIDGET] ❌ Erreur chargement agences: $e');
    }
  }

  /// 🔍 Filtrer les utilisateurs
  List<Map<String, dynamic>> _getFilteredUsers() {
    return _users.where((user) {
      // Filtre par recherche
      if (_searchQuery.isNotEmpty) {
        final name = '${user['prenom'] ?? ''} ${user['nom'] ?? ''}'.toLowerCase();
        final email = (user['email'] ?? '').toLowerCase();
        final search = _searchQuery.toLowerCase();
        
        if (!name.contains(search) && !email.contains(search)) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  /// ➕ Afficher le formulaire de création
  void _showCreateUserForm() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 600,
          padding: const EdgeInsets.all(24),
          child: UserCreateFormWidget(
            compagnies: _compagnies,
            agences: _agences,
            onUserCreated: () {
              Navigator.of(context).pop();
              _loadUsers();
            },
          ),
        ),
      ),
    );
  }

  /// ✏️ Afficher le formulaire d'édition
  void _showEditUserDialog(Map<String, dynamic> user) {
    showDialog(
      context: context,
      builder: (context) => UserEditDialog(
        user: user,
        compagnies: _compagnies,
        agences: _agences,
        onUserUpdated: () {
          Navigator.of(context).pop();
          _loadUsers();
        },
      ),
    );
  }

  /// 🗑️ Supprimer un utilisateur
  Future<void> _deleteUser(String userId, String userEmail) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text('Êtes-vous sûr de vouloir supprimer l\'utilisateur $userEmail ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final result = await UserManagementService.deleteUser(userId);
        if (result == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Utilisateur supprimé avec succès'),
              backgroundColor: Colors.green,
            ),
          );
          _loadUsers();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: $result'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 🎨 Obtenir la couleur du statut
  Color _getStatusColor(String? status) {
    switch (status) {
      case 'actif':
        return Colors.green;
      case 'en_attente':
        return Colors.orange;
      case 'desactive':
        return Colors.red;
      case 'supprime':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 🏷️ Obtenir le nom d'affichage du rôle
  String _getRoleDisplayName(String? role) {
    switch (role) {
      case 'super_admin':
        return 'Super Admin';
      case 'admin_compagnie':
        return 'Admin Compagnie';
      case 'admin_agence':
        return 'Admin Agence';
      case 'agent_agence':
        return 'Agent';
      case 'expert_auto':
        return 'Expert Auto';
      case 'conducteur':
        return 'Conducteur';
      default:
        return role ?? 'Inconnu';
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredUsers = _getFilteredUsers();

    return Column(
      children: [
        // En-tête avec bouton d'ajout
        Row(
          children: [
            const Text(
              'Gestion des Utilisateurs',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            ElevatedButton.icon(
              onPressed: _showCreateUserForm,
              icon: const Icon(Icons.add),
              label: const Text('Nouvel Utilisateur'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Filtres
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Première ligne de filtres
                Row(
                  children: [
                    // Recherche
                    Expanded(
                      flex: 2,
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: 'Rechercher',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          setState(() => _searchQuery = value);
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Filtre par rôle
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Rôle',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedRole,
                        items: [
                          const DropdownMenuItem(value: null, child: Text('Tous')),
                          ..._roles.map((role) => DropdownMenuItem(
                            value: role,
                            child: Text(_getRoleDisplayName(role)),
                          )),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedRole = value);
                          _loadUsers();
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Filtre par statut
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Statut',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedStatus,
                        items: [
                          const DropdownMenuItem(value: null, child: Text('Tous')),
                          ..._statuses.map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.toUpperCase()),
                          )),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedStatus = value);
                          _loadUsers();
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                // Deuxième ligne de filtres
                Row(
                  children: [
                    // Filtre par compagnie
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Compagnie',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedCompagnie,
                        items: [
                          const DropdownMenuItem(value: null, child: Text('Toutes')),
                          ..._compagnies.map((compagnie) => DropdownMenuItem(
                            value: compagnie['id'],
                            child: Text(compagnie['nom'] ?? 'Sans nom'),
                          )),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedCompagnie = value;
                            _selectedAgence = null; // Reset agence
                          });
                          _loadUsers();
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Filtre par agence
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Agence',
                          border: OutlineInputBorder(),
                        ),
                        value: _selectedAgence,
                        items: [
                          const DropdownMenuItem(value: null, child: Text('Toutes')),
                          ..._agences
                              .where((agence) => _selectedCompagnie == null || 
                                     agence['compagnieId'] == _selectedCompagnie)
                              .map((agence) => DropdownMenuItem(
                                value: agence['id'],
                                child: Text(agence['nom'] ?? 'Sans nom'),
                              )),
                        ],
                        onChanged: (value) {
                          setState(() => _selectedAgence = value);
                          _loadUsers();
                        },
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    // Bouton de réinitialisation
                    ElevatedButton.icon(
                      onPressed: () {
                        setState(() {
                          _selectedRole = null;
                          _selectedCompagnie = null;
                          _selectedAgence = null;
                          _selectedStatus = null;
                          _searchQuery = '';
                        });
                        _loadUsers();
                      },
                      icon: const Icon(Icons.clear),
                      label: const Text('Réinitialiser'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Liste des utilisateurs
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : filteredUsers.isEmpty
                  ? const Center(
                      child: Text(
                        'Aucun utilisateur trouvé',
                        style: TextStyle(fontSize: 16),
                      ),
                    )
                  : Card(
                      child: ListView.builder(
                        itemCount: filteredUsers.length,
                        itemBuilder: (context, index) {
                          final user = filteredUsers[index];
                          return _buildUserTile(user);
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  /// 🎨 Construire une tuile utilisateur
  Widget _buildUserTile(Map<String, dynamic> user) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getStatusColor(user['status']),
        child: Text(
          (user['prenom']?.toString().isNotEmpty == true 
              ? user['prenom'][0] 
              : user['email']?[0] ?? '?').toUpperCase(),
          style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      title: Text(
        '${user['prenom'] ?? ''} ${user['nom'] ?? ''}'.trim().isNotEmpty
            ? '${user['prenom'] ?? ''} ${user['nom'] ?? ''}'
            : user['email'] ?? 'Sans nom',
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('📧 ${user['email'] ?? 'Pas d\'email'}'),
          Text('🏷️ ${_getRoleDisplayName(user['role'])}'),
          if (user['compagnieNom'] != null)
            Text('🏢 ${user['compagnieNom']}'),
          if (user['agenceNom'] != null)
            Text('🏪 ${user['agenceNom']}'),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Statut
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(user['status']),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              (user['status'] ?? 'inconnu').toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Actions
          PopupMenuButton<String>(
            onSelected: (action) {
              switch (action) {
                case 'edit':
                  _showEditUserDialog(user);
                  break;
                case 'delete':
                  _deleteUser(user['id'], user['email'] ?? '');
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Modifier'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Supprimer', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      isThreeLine: true,
    );
  }
}
