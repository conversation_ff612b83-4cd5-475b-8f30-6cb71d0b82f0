import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/compagnie_assurance.dart';

/// 🏢 Service de gestion des compagnies d'assurance
class CompagnieService {
  static const String _collection = 'compagnies_assurance';
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 📋 Méthode statique pour compatibilité avec l'ancien code
  static Future<List<Map<String, dynamic>>> getAllCompagnies() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection(_collection)
          .orderBy('nom')
          .get();

      return snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      debugPrint('[COMPAGNIE_SERVICE] ❌ Erreur getAllCompagnies: $e');
      return [];
    }
  }

  /// 📋 Obtenir toutes les compagnies avec pagination (incluant données de test)
  Stream<List<CompagnieAssurance>> getCompagnies({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? searchQuery,
    bool? activeOnly,
    bool includeTestData = true, // 🎲 Inclure les données de test par défaut
  }) {
    Query query = _firestore.collection(_collection);

    // Tri par nom (sans filtre isActive pour éviter l'erreur d'index)
    query = query.orderBy('nom');

    // Pagination
    if (startAfter != null) {
      query = query.startAfterDocument(startAfter);
    }

    query = query.limit(limit);

    return query.snapshots().map((snapshot) {
      // 🎲 Filtrage des données de test au niveau des documents
      var filteredDocs = snapshot.docs;

      if (!includeTestData) {
        // Exclure les données de test
        filteredDocs = snapshot.docs.where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          // Vérifier si c'est une donnée de test (commence par test_ ou a isFakeData = true)
          return !doc.id.startsWith('test_') && !(data['isFakeData'] == true);
        }).toList();
      }

      var compagnies = filteredDocs
          .map((doc) => CompagnieAssurance.fromFirestore(doc))
          .toList();

      // Filtrage par statut actif côté client
      if (activeOnly == true) {
        // Afficher seulement les inactives
        compagnies = compagnies.where((compagnie) => !compagnie.isActive).toList();
      } else if (activeOnly == false) {
        // Afficher seulement les actives
        compagnies = compagnies.where((compagnie) => compagnie.isActive).toList();
      }
      // Si activeOnly == null, afficher toutes les compagnies

      // Filtrage par recherche côté client (pour la simplicité)
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        compagnies = compagnies.where((compagnie) {
          return compagnie.nom.toLowerCase().contains(searchLower) ||
                 compagnie.code.toLowerCase().contains(searchLower) ||
                 compagnie.ville.toLowerCase().contains(searchLower) ||
                 compagnie.gouvernorat.toLowerCase().contains(searchLower);
        }).toList();
      }

      // Trier par statut (actives en premier) puis par nom
      compagnies.sort((a, b) {
        if (a.isActive && !b.isActive) return -1;
        if (!a.isActive && b.isActive) return 1;
        return a.nom.compareTo(b.nom);
      });

      return compagnies;
    });
  }

  /// 🔍 Obtenir une compagnie par ID
  Future<CompagnieAssurance?> getCompagnieById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      if (doc.exists) {
        return CompagnieAssurance.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getCompagnieById: $e');
      return null;
    }
  }

  /// 🔍 Vérifier l'unicité du code compagnie
  Future<bool> isCodeUnique(String code, {String? excludeId}) async {
    try {
      final query = await _firestore
          .collection(_collection)
          .where('code', isEqualTo: code.toUpperCase())
          .get();

      if (excludeId != null) {
        // Exclure l'ID actuel lors de la modification
        return query.docs.every((doc) => doc.id == excludeId);
      }

      return query.docs.isEmpty;
    } catch (e) {
      debugPrint('[CompagnieService] Erreur isCodeUnique: $e');
      return false;
    }
  }

  /// ➕ Créer une nouvelle compagnie
  Future<String?> createCompagnie(CompagnieAssurance compagnie) async {
    try {
      // Vérifier l'unicité du code
      final isUnique = await isCodeUnique(compagnie.code);
      if (!isUnique) {
        throw Exception('Le code "${compagnie.code}" existe déjà');
      }

      final docRef = await _firestore.collection(_collection).add(compagnie.toFirestore());
      
      debugPrint('[CompagnieService] Compagnie créée: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('[CompagnieService] Erreur createCompagnie: $e');
      rethrow;
    }
  }

  /// ✏️ Modifier une compagnie existante
  Future<void> updateCompagnie(String id, CompagnieAssurance compagnie) async {
    try {
      // Vérifier l'unicité du code (en excluant l'ID actuel)
      final isUnique = await isCodeUnique(compagnie.code, excludeId: id);
      if (!isUnique) {
        throw Exception('Le code "${compagnie.code}" existe déjà');
      }

      final updatedData = compagnie.toFirestore();
      updatedData['dateModification'] = Timestamp.now();

      await _firestore.collection(_collection).doc(id).update(updatedData);
      
      debugPrint('[CompagnieService] Compagnie modifiée: $id');
    } catch (e) {
      debugPrint('[CompagnieService] Erreur updateCompagnie: $e');
      rethrow;
    }
  }

  /// 🗑️ Supprimer une compagnie (soft delete)
  Future<void> deleteCompagnie(String id) async {
    try {
      // Vérifier s'il y a des agences liées
      final agencesCount = await getAgencesCount(id);
      if (agencesCount > 0) {
        throw Exception('Impossible de supprimer: $agencesCount agence(s) liée(s)');
      }

      // Soft delete - marquer comme inactive
      await _firestore.collection(_collection).doc(id).update({
        'isActive': false,
        'dateModification': Timestamp.now(),
      });
      
      debugPrint('[CompagnieService] Compagnie supprimée (soft): $id');
    } catch (e) {
      debugPrint('[CompagnieService] Erreur deleteCompagnie: $e');
      rethrow;
    }
  }

  /// 🗑️ Supprimer définitivement une compagnie
  Future<void> hardDeleteCompagnie(String id) async {
    try {
      // Vérifier s'il y a des agences liées
      final agencesCount = await getAgencesCount(id);
      if (agencesCount > 0) {
        throw Exception('Impossible de supprimer: $agencesCount agence(s) liée(s)');
      }

      await _firestore.collection(_collection).doc(id).delete();
      
      debugPrint('[CompagnieService] Compagnie supprimée définitivement: $id');
    } catch (e) {
      debugPrint('[CompagnieService] Erreur hardDeleteCompagnie: $e');
      rethrow;
    }
  }

  /// 🔄 Restaurer une compagnie supprimée
  Future<void> restoreCompagnie(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).update({
        'isActive': true,
        'dateModification': Timestamp.now(),
      });
      
      debugPrint('[CompagnieService] Compagnie restaurée: $id');
    } catch (e) {
      debugPrint('[CompagnieService] Erreur restoreCompagnie: $e');
      rethrow;
    }
  }

  /// 📊 Obtenir le nombre d'agences liées à une compagnie
  Future<int> getAgencesCount(String compagnieId) async {
    try {
      final snapshot = await _firestore
          .collection('agences')
          .where('compagnieId', isEqualTo: compagnieId)
          .where('isActive', isEqualTo: true)
          .get();
      
      return snapshot.docs.length;
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getAgencesCount: $e');
      return 0;
    }
  }

  /// 📊 Obtenir les statistiques d'une compagnie
  Future<Map<String, dynamic>> getCompagnieStats(String compagnieId) async {
    try {
      final futures = await Future.wait([
        // Nombre d'agences
        _firestore
            .collection('agences')
            .where('compagnieId', isEqualTo: compagnieId)
            .where('isActive', isEqualTo: true)
            .get(),
        
        // Nombre d'agents
        _firestore
            .collection('agents_assurance')
            .where('compagnieId', isEqualTo: compagnieId)
            .where('isActive', isEqualTo: true)
            .get(),
        
        // Nombre de contrats
        _firestore
            .collection('contrats')
            .where('compagnieId', isEqualTo: compagnieId)
            .where('isActive', isEqualTo: true)
            .get(),
      ]);

      return {
        'agences': futures[0].docs.length,
        'agents': futures[1].docs.length,
        'contrats': futures[2].docs.length,
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getCompagnieStats: $e');
      return {
        'agences': 0,
        'agents': 0,
        'contrats': 0,
        'lastUpdated': DateTime.now(),
      };
    }
  }

  /// 📈 Obtenir les statistiques globales des compagnies
  Future<Map<String, dynamic>> getGlobalStats() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      
      final actives = snapshot.docs.where((doc) =>
          doc.data()['isActive'] == true).length;
      
      final inactives = snapshot.docs.length - actives;

      return {
        'total': snapshot.docs.length,
        'actives': actives,
        'inactives': inactives,
        'lastUpdated': DateTime.now(),
      };
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getGlobalStats: $e');
      return {
        'total': 0,
        'actives': 0,
        'inactives': 0,
        'lastUpdated': DateTime.now(),
      };
    }
  }

  /// 🔍 Rechercher des compagnies
  Future<List<CompagnieAssurance>> searchCompagnies(String query) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final searchLower = query.toLowerCase();
      
      return snapshot.docs
          .map((doc) => CompagnieAssurance.fromFirestore(doc))
          .where((compagnie) {
            return compagnie.nom.toLowerCase().contains(searchLower) ||
                   compagnie.code.toLowerCase().contains(searchLower) ||
                   compagnie.ville.toLowerCase().contains(searchLower) ||
                   compagnie.gouvernorat.toLowerCase().contains(searchLower);
          })
          .toList();
    } catch (e) {
      debugPrint('[CompagnieService] Erreur searchCompagnies: $e');
      return [];
    }
  }

  /// 📋 Obtenir les compagnies pour dropdown (Future - pour compatibilité)
  Future<List<Map<String, dynamic>>> getCompagniesForDropdown() async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .orderBy('nom')
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'nom': data['nom'] ?? '',
          'code': data['code'] ?? '',
        };
      }).toList();
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getCompagniesForDropdown: $e');
      return [];
    }
  }

  /// 📋 Stream des compagnies pour dropdown (mise à jour en temps réel)
  Stream<List<Map<String, dynamic>>> getCompagniesForDropdownStream() {
    try {
      return _firestore
          .collection(_collection)
          .orderBy('nom')
          .snapshots()
          .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          return {
            'id': doc.id,
            'nom': data['nom'] ?? '',
            'code': data['code'] ?? '',
          };
        }).toList();
      });
    } catch (e) {
      debugPrint('[CompagnieService] Erreur getCompagniesForDropdownStream: $e');
      return Stream.value([]);
    }
  }
}
