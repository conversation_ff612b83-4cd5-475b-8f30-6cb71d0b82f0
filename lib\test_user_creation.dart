import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'features/admin/services/user_creation_test_service.dart';

/// 🧪 Application de test pour la création d'utilisateurs
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');
  } catch (e) {
    print('❌ Erreur Firebase: $e');
  }
  
  runApp(const TestUserCreationApp());
}

class TestUserCreationApp extends StatelessWidget {
  const TestUserCreationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Création Utilisateurs',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const TestUserCreationScreen(),
    );
  }
}

class TestUserCreationScreen extends StatefulWidget {
  const TestUserCreationScreen({super.key});

  @override
  State<TestUserCreationScreen> createState() => _TestUserCreationScreenState();
}

class _TestUserCreationScreenState extends State<TestUserCreationScreen> {
  final _emailController = TextEditingController();
  final _nomController = TextEditingController();
  final _prenomController = TextEditingController();
  final _phoneController = TextEditingController();
  
  bool _isLoading = false;
  Map<String, dynamic>? _lastResult;

  @override
  void initState() {
    super.initState();
    // Valeurs par défaut pour les tests
    _emailController.text = '<EMAIL>';
    _nomController.text = 'Dupont';
    _prenomController.text = 'Jean';
    _phoneController.text = '+216 98 123 456';
  }

  @override
  void dispose() {
    _emailController.dispose();
    _nomController.dispose();
    _prenomController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  /// 🧪 Tester la création d'un admin compagnie
  Future<void> _testCreateAdminCompagnie() async {
    setState(() => _isLoading = true);

    try {
      final result = await UserCreationTestService.testCreateAdminCompagnie(
        email: _emailController.text.trim(),
        nom: _nomController.text.trim(),
        prenom: _prenomController.text.trim(),
        compagnieId: 'test_compagnie_id',
        compagnieNom: 'STAR Assurance Test',
        phone: _phoneController.text.trim().isNotEmpty 
            ? _phoneController.text.trim() 
            : null,
      );

      setState(() {
        _lastResult = UserCreationTestService.getTestReport(result);
      });

      if (result['success'] == true) {
        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result['error'] ?? 'Erreur inconnue');
      }

    } catch (e) {
      _showErrorDialog('Exception: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// ✅ Afficher le dialog de succès
  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Succès !'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('✅ Compte créé avec succès'),
            const SizedBox(height: 8),
            Text('🆔 UID: ${result['uid']}'),
            const SizedBox(height: 8),
            Text('📧 Email: ${result['email']}'),
            const SizedBox(height: 8),
            Text('🔑 Mot de passe: ${result['temporaryPassword']}'),
            const SizedBox(height: 8),
            Text('📬 Email envoyé: ${result['emailSent'] ? '✅' : '❌'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// ❌ Afficher le dialog d'erreur
  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Erreur'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Test Création Utilisateurs'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Formulaire de test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📝 Données de test',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    TextField(
                      controller: _prenomController,
                      decoration: const InputDecoration(
                        labelText: 'Prénom',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    TextField(
                      controller: _nomController,
                      decoration: const InputDecoration(
                        labelText: 'Nom',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    TextField(
                      controller: _phoneController,
                      decoration: const InputDecoration(
                        labelText: 'Téléphone (optionnel)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Bouton de test
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _testCreateAdminCompagnie,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        '🧪 Tester Création Admin Compagnie',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Résultats du dernier test
            if (_lastResult != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '📊 Dernier résultat',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12),
                      Text('Succès: ${_lastResult!['success'] ? '✅' : '❌'}'),
                      if (_lastResult!['uid'] != null)
                        Text('UID: ${_lastResult!['uid']}'),
                      if (_lastResult!['email'] != null)
                        Text('Email: ${_lastResult!['email']}'),
                      Text('Mot de passe généré: ${_lastResult!['passwordGenerated'] ? '✅' : '❌'}'),
                      Text('Email envoyé: ${_lastResult!['emailSent'] ? '✅' : '❌'}'),
                      if (_lastResult!['error'] != null)
                        Text('Erreur: ${_lastResult!['error']}', 
                             style: const TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
