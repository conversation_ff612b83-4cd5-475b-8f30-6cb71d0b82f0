import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import '../models/agence_assurance.dart';
import 'admin_firestore_service.dart';

/// 🏪 Service pour la gestion des agences d'assurance
class AgenceService {
  static const String _collection = 'agences';
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// 📋 Méthode statique pour compatibilité avec l'ancien code
  static Future<List<Map<String, dynamic>>> getAllAgences() async {
    try {
      final snapshot = await _firestore
          .collection('agences_assurance')
          .orderBy('nom')
          .get();

      return snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur getAllAgences: $e');
      return [];
    }
  }

  /// 📋 Obtenir les agences avec pagination et filtres (incluant données de test)
  Stream<List<AgenceAssurance>> getAgences({
    int limit = 20,
    DocumentSnapshot? startAfter,
    String? searchQuery,
    String? compagnieId,
    String? gouvernorat,
    bool? activeOnly,
    bool includeTestData = true, // 🎲 Inclure les données de test par défaut
  }) {
    try {
      debugPrint('[AGENCE_SERVICE] 📥 Récupération agences - limit: $limit, compagnie: $compagnieId, includeTestData: $includeTestData');

      // Si on inclut les données de test, utiliser la collection agences_assurance
      final collectionName = includeTestData ? 'agences_assurance' : _collection;
      Query query = _firestore.collection(collectionName);

      // Filtrer par compagnie si spécifié
      if (compagnieId != null && compagnieId.isNotEmpty) {
        query = query.where('compagnieId', isEqualTo: compagnieId);
      }

      // Filtrer par gouvernorat si spécifié
      if (gouvernorat != null && gouvernorat.isNotEmpty) {
        query = query.where('gouvernorat', isEqualTo: gouvernorat);
      }

      // Filtrer par statut actif si spécifié
      // Note: On ne filtre pas ici car les agences peuvent avoir 'active' ou 'isActive'
      // Le filtrage se fera côté client

      // Recherche textuelle (limitation Firestore)
      if (searchQuery != null && searchQuery.isNotEmpty) {
        // Pour la recherche, on utilise un range query sur le nom
        final searchEnd = searchQuery.substring(0, searchQuery.length - 1) +
            String.fromCharCode(searchQuery.codeUnitAt(searchQuery.length - 1) + 1);
        query = query
            .where('nom', isGreaterThanOrEqualTo: searchQuery)
            .where('nom', isLessThan: searchEnd);
      }

      // Tri par nom
      query = query.orderBy('nom');

      // Pagination
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      return query.snapshots().map((snapshot) {
        var agences = snapshot.docs
            .map((doc) => AgenceAssurance.fromFirestore(doc))
            .toList();

        // Filtrage par statut actif côté client (pour gérer 'active' et 'isActive')
        if (activeOnly == true) {
          agences = agences.where((agence) => agence.isActive).toList();
        }

        // Filtrage côté client pour la recherche avancée
        if (searchQuery != null && searchQuery.isNotEmpty) {
          agences = agences.where((agence) => agence.matchesSearch(searchQuery)).toList();
        }

        debugPrint('[AGENCE_SERVICE] ✅ ${agences.length} agences récupérées');
        return agences;
      });

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur récupération agences: $e');
      return Stream.value([]);
    }
  }

  /// ➕ Créer une nouvelle agence
  Future<String?> createAgence(AgenceAssurance agence) async {
    try {
      debugPrint('[AGENCE_SERVICE] ➕ Création agence: ${agence.nom}');

      // Validation
      final validationError = agence.validate();
      if (validationError != null) {
        return validationError;
      }

      // Vérifier l'unicité du code
      final isCodeUnique = await this.isCodeUnique(agence.code);
      if (!isCodeUnique) {
        return 'Ce code d\'agence existe déjà';
      }

      // Créer l'agence
      await _firestore.collection(_collection).add(agence.toFirestore());

      debugPrint('[AGENCE_SERVICE] ✅ Agence créée avec succès');
      return null; // Succès

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur création agence: $e');
      return 'Erreur lors de la création: $e';
    }
  }

  /// ✏️ Mettre à jour une agence
  Future<String?> updateAgence(AgenceAssurance agence) async {
    try {
      debugPrint('[AGENCE_SERVICE] ✏️ Mise à jour agence: ${agence.id}');

      // Validation
      final validationError = agence.validate();
      if (validationError != null) {
        return validationError;
      }

      // Vérifier l'unicité du code (exclure l'agence actuelle)
      final isCodeUnique = await this.isCodeUnique(agence.code, excludeId: agence.id);
      if (!isCodeUnique) {
        return 'Ce code d\'agence existe déjà';
      }

      // Mettre à jour avec la date de modification
      final updatedAgence = agence.copyWith(
        dateModification: DateTime.now(),
      );

      await _firestore
          .collection(_collection)
          .doc(agence.id)
          .update(updatedAgence.toFirestore());

      debugPrint('[AGENCE_SERVICE] ✅ Agence mise à jour avec succès');
      return null; // Succès

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur mise à jour agence: $e');
      return 'Erreur lors de la mise à jour: $e';
    }
  }

  /// 🗑️ Supprimer une agence (soft delete)
  Future<String?> deleteAgence(String agenceId) async {
    try {
      debugPrint('[AGENCE_SERVICE] 🗑️ Suppression agence: $agenceId');

      await _firestore.collection(_collection).doc(agenceId).update({
        'isActive': false,
        'dateModification': Timestamp.now(),
      });

      debugPrint('[AGENCE_SERVICE] ✅ Agence supprimée avec succès');
      return null; // Succès

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur suppression agence: $e');
      return 'Erreur lors de la suppression: $e';
    }
  }

  /// 🔄 Restaurer une agence
  Future<String?> restoreAgence(String agenceId) async {
    try {
      debugPrint('[AGENCE_SERVICE] 🔄 Restauration agence: $agenceId');

      await _firestore.collection(_collection).doc(agenceId).update({
        'isActive': true,
        'dateModification': Timestamp.now(),
      });

      debugPrint('[AGENCE_SERVICE] ✅ Agence restaurée avec succès');
      return null; // Succès

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur restauration agence: $e');
      return 'Erreur lors de la restauration: $e';
    }
  }

  /// 🔍 Obtenir une agence par ID
  Future<AgenceAssurance?> getAgenceById(String agenceId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(agenceId).get();
      
      if (doc.exists) {
        return AgenceAssurance.fromFirestore(doc);
      }
      return null;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur récupération agence: $e');
      return null;
    }
  }

  /// 🔒 Vérifier l'unicité du code
  Future<bool> isCodeUnique(String code, {String? excludeId}) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .where('code', isEqualTo: code);

      final snapshot = await query.get();
      
      if (excludeId != null) {
        return snapshot.docs.every((doc) => doc.id == excludeId);
      }
      
      return snapshot.docs.isEmpty;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur vérification unicité: $e');
      return false;
    }
  }

  /// 📊 Obtenir les statistiques d'une agence
  Future<Map<String, dynamic>> getAgenceStats(String agenceId) async {
    try {
      debugPrint('[AGENCE_SERVICE] 📊 Récupération stats agence: $agenceId');

      // Compter les agents de cette agence
      final agentsSnapshot = await _firestore
          .collection('agents_assurance')
          .where('agenceId', isEqualTo: agenceId)
          .get();

      // Compter les contrats de cette agence
      final contratsSnapshot = await _firestore
          .collection('contrats')
          .where('agenceId', isEqualTo: agenceId)
          .get();

      // Compter les constats de cette agence
      final constatsSnapshot = await _firestore
          .collection('constats')
          .where('agenceId', isEqualTo: agenceId)
          .get();

      final stats = {
        'totalAgents': agentsSnapshot.docs.length,
        'totalContrats': contratsSnapshot.docs.length,
        'totalConstats': constatsSnapshot.docs.length,
        'agentsActifs': agentsSnapshot.docs
            .where((doc) => doc.data()['isActive'] == true)
            .length,
        'contratsActifs': contratsSnapshot.docs
            .where((doc) => doc.data()['isActive'] == true)
            .length,
      };

      debugPrint('[AGENCE_SERVICE] ✅ Stats agence récupérées: $stats');
      return stats;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur stats agence: $e');
      return {
        'totalAgents': 0,
        'totalContrats': 0,
        'totalConstats': 0,
        'agentsActifs': 0,
        'contratsActifs': 0,
      };
    }
  }

  /// 📋 Obtenir les agences pour dropdown
  Future<List<Map<String, String>>> getAgencesForDropdown({String? compagnieId}) async {
    try {
      debugPrint('[AGENCE_SERVICE] 🔍 Recherche agences dropdown - compagnieId: $compagnieId');

      Query query = _firestore.collection(_collection);

      // Si compagnieId spécifié, filtrer d'abord par compagnie
      if (compagnieId != null) {
        query = query.where('compagnieId', isEqualTo: compagnieId);
      }

      // Ne pas utiliser orderBy pour éviter le problème d'index
      final snapshot = await query.get();
      debugPrint('[AGENCE_SERVICE] 📊 ${snapshot.docs.length} agences trouvées dans Firestore');

      final agences = snapshot.docs.where((doc) {
        final data = doc.data() as Map<String, dynamic>;
        // Gérer les deux champs possibles : 'isActive' et 'active'
        final isActive = data['isActive'] ?? data['active'] ?? true;
        return isActive == true;
      }).map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          'nom': data['nom'] as String? ?? 'Nom non défini',
          'code': data['code'] as String? ?? 'Code non défini',
          'ville': data['ville'] as String? ?? 'Ville non définie',
        };
      }).toList();

      // Trier côté client
      agences.sort((a, b) => a['nom']!.compareTo(b['nom']!));

      debugPrint('[AGENCE_SERVICE] ✅ ${agences.length} agences actives retournées');
      return agences;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur dropdown agences: $e');

      // Si erreur d'index, essayer une requête plus simple
      if (e.toString().contains('index')) {
        debugPrint('[AGENCE_SERVICE] 🔄 Tentative avec requête simplifiée...');
        return await _getAgencesSimpleQuery(compagnieId);
      }

      return [];
    }
  }

  /// 📋 Requête simplifiée sans orderBy pour éviter les problèmes d'index
  Future<List<Map<String, String>>> _getAgencesSimpleQuery(String? compagnieId) async {
    try {
      debugPrint('[AGENCE_SERVICE] 🔄 Requête simplifiée - compagnieId: $compagnieId');

      // Requête très simple : juste récupérer tous les documents
      final snapshot = await _firestore.collection(_collection).get();
      debugPrint('[AGENCE_SERVICE] 📊 ${snapshot.docs.length} documents récupérés');

      final agences = <Map<String, String>>[];

      for (var doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;

        // Filtrer par compagnie si spécifié
        final docCompagnieId = data['compagnieId'] ?? data['compagnie'];
        if (compagnieId != null && docCompagnieId != compagnieId) {
          continue;
        }

        // Vérifier si actif
        final isActive = data['isActive'] ?? data['active'] ?? data['statut'] == 'active' ?? true;
        if (!isActive) {
          continue;
        }

        agences.add({
          'id': doc.id,
          'nom': data['nom']?.toString() ?? 'Nom non défini',
          'code': data['code']?.toString() ?? 'Code non défini',
          'ville': data['ville']?.toString() ?? 'Ville non définie',
        });
      }

      // Trier côté client
      agences.sort((a, b) => a['nom']!.compareTo(b['nom']!));

      debugPrint('[AGENCE_SERVICE] ✅ ${agences.length} agences trouvées avec requête simplifiée');
      return agences;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur requête simplifiée: $e');
      return [];
    }
  }

  /// 🔍 Méthode de test pour diagnostiquer les problèmes d'agences
  Future<void> debugAgencesInFirestore() async {
    try {
      debugPrint('[AGENCE_SERVICE] 🔍 === DIAGNOSTIC AGENCES FIRESTORE ===');

      // 1. Compter toutes les agences
      final allSnapshot = await _firestore.collection(_collection).get();
      debugPrint('[AGENCE_SERVICE] 📊 Total documents dans collection "$_collection": ${allSnapshot.docs.length}');

      // 2. Analyser chaque agence
      for (var doc in allSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        debugPrint('[AGENCE_SERVICE] 📋 Agence ${doc.id}:');
        debugPrint('  - nom: ${data['nom']}');
        debugPrint('  - compagnieId: ${data['compagnieId']}');
        debugPrint('  - isActive: ${data['isActive']}');
        debugPrint('  - active: ${data['active']}');
        debugPrint('  - ville: ${data['ville']}');
      }

      // 3. Tester les requêtes avec différents filtres
      final activeQuery1 = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();
      debugPrint('[AGENCE_SERVICE] 🔍 Agences avec isActive=true: ${activeQuery1.docs.length}');

      final activeQuery2 = await _firestore
          .collection(_collection)
          .where('active', isEqualTo: true)
          .get();
      debugPrint('[AGENCE_SERVICE] 🔍 Agences avec active=true: ${activeQuery2.docs.length}');

      debugPrint('[AGENCE_SERVICE] 🔍 === FIN DIAGNOSTIC ===');

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur diagnostic: $e');
    }
  }

  /// 🏗️ Créer une agence de test pour STAR Assurance
  Future<void> createTestAgenceForStar() async {
    try {
      debugPrint('[AGENCE_SERVICE] 🏗️ Création agence de test pour STAR...');

      // Chercher l'ID de STAR Assurance
      final compagniesSnapshot = await _firestore.collection('compagnies_assurance').get();
      String? starCompagnieId;

      for (var doc in compagniesSnapshot.docs) {
        final data = doc.data();
        if (data['nom']?.toString().contains('STAR') == true ||
            data['code']?.toString().contains('STAR') == true) {
          starCompagnieId = doc.id;
          debugPrint('[AGENCE_SERVICE] 🎯 STAR trouvée avec ID: $starCompagnieId');
          break;
        }
      }

      if (starCompagnieId == null) {
        debugPrint('[AGENCE_SERVICE] ❌ STAR Assurance non trouvée');
        return;
      }

      // Créer l'agence de test
      final agenceData = {
        'nom': 'STAR Tunis Centre Test',
        'code': 'STAR-TUN-TEST',
        'compagnieId': starCompagnieId,
        'compagnieNom': 'STAR Assurance',
        'adresse': 'Avenue Habib Bourguiba, Tunis',
        'ville': 'Tunis',
        'gouvernorat': 'Tunis',
        'telephone': '+216 71 123 456',
        'email': '<EMAIL>',
        'responsable': 'Ahmed Ben Ali',
        'zone': 'Centre Tunis',
        'dateCreation': Timestamp.now(),
        'dateModification': Timestamp.now(),
        'isActive': true,
        'active': true, // Pour compatibilité
      };

      final docRef = await _firestore.collection(_collection).add(agenceData);
      debugPrint('[AGENCE_SERVICE] ✅ Agence de test créée avec ID: ${docRef.id}');

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur création agence test: $e');
    }
  }

  /// 🔄 Méthode alternative pour obtenir les agences (toutes collections)
  Future<List<Map<String, String>>> getAgencesFromAllCollections({String? compagnieId}) async {
    try {
      debugPrint('[AGENCE_SERVICE] 🔍 Recherche agences dans toutes les collections...');

      List<Map<String, String>> allAgences = [];

      // Collection 1: 'agences'
      try {
        final snapshot1 = await _firestore.collection('agences').get();
        debugPrint('[AGENCE_SERVICE] 📊 Collection "agences": ${snapshot1.docs.length} documents');

        for (var doc in snapshot1.docs) {
          final data = doc.data();
          final isActive = data['isActive'] ?? data['active'] ?? data['statut'] == 'active' ?? true;
          final docCompagnieId = data['compagnieId'] ?? data['compagnie'];

          if (isActive && (compagnieId == null || docCompagnieId == compagnieId)) {
            allAgences.add({
              'id': doc.id,
              'nom': data['nom']?.toString() ?? 'Nom non défini',
              'code': data['code']?.toString() ?? 'Code non défini',
              'ville': data['ville']?.toString() ?? 'Ville non définie',
              'compagnieId': docCompagnieId?.toString() ?? '',
            });
          }
        }
      } catch (e) {
        debugPrint('[AGENCE_SERVICE] ⚠️ Erreur collection "agences": $e');
      }

      // Collection 2: 'agences_assurance'
      try {
        final snapshot2 = await _firestore.collection('agences_assurance').get();
        debugPrint('[AGENCE_SERVICE] 📊 Collection "agences_assurance": ${snapshot2.docs.length} documents');

        for (var doc in snapshot2.docs) {
          final data = doc.data();
          final isActive = data['actif'] ?? data['isActive'] ?? data['active'] ?? true;
          final docCompagnieId = data['compagnieId'] ?? data['compagnie'];

          if (isActive && (compagnieId == null || docCompagnieId == compagnieId)) {
            allAgences.add({
              'id': doc.id,
              'nom': data['nom']?.toString() ?? 'Nom non défini',
              'code': data['code']?.toString() ?? 'Code non défini',
              'ville': data['ville']?.toString() ?? 'Ville non définie',
              'compagnieId': docCompagnieId?.toString() ?? '',
            });
          }
        }
      } catch (e) {
        debugPrint('[AGENCE_SERVICE] ⚠️ Erreur collection "agences_assurance": $e');
      }

      debugPrint('[AGENCE_SERVICE] ✅ Total agences trouvées: ${allAgences.length}');
      return allAgences;

    } catch (e) {
      debugPrint('[AGENCE_SERVICE] ❌ Erreur recherche globale: $e');
      return [];
    }
  }

  /// 🎯 Générer un code d'agence automatique
  Future<String> generateAgenceCode(String compagnieCode, String gouvernorat) async {
    try {
      // Utiliser la méthode centralisée dans AdminFirestoreService
      return await AdminFirestoreService.generateAgenceCode(gouvernorat, compagnieCode);
    } catch (e) {
      print('Erreur lors de la génération du code agence: $e');
      // Fallback avec format simple
      final gouvernoratCode = gouvernorat.substring(0, 3).toUpperCase();
      return '$compagnieCode-$gouvernoratCode-${DateTime.now().millisecondsSinceEpoch.toString().substring(10)}';
    }
  }
}
