import 'package:flutter/foundation.dart';
import 'admin_creation_service.dart';
import 'email_notification_service.dart';

/// 🧪 Service de test pour la création d'utilisateurs
class UserCreationTestService {
  
  /// 🧪 Tester la création complète d'un admin compagnie
  static Future<Map<String, dynamic>> testCreateAdminCompagnie({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String compagnieNom,
    String? phone,
  }) async {
    try {
      debugPrint('[USER_CREATION_TEST] 🧪 Test création Admin Compagnie...');
      debugPrint('[USER_CREATION_TEST]   - Email: $email');
      debugPrint('[USER_CREATION_TEST]   - Nom: $prenom $nom');
      debugPrint('[USER_CREATION_TEST]   - Compagnie: $compagnieNom ($compagnieId)');

      // 1. Générer un mot de passe temporaire
      final motDePasseTemporaire = _generateTemporaryPassword();
      debugPrint('[USER_CREATION_TEST] 🔑 Mot de passe généré: $motDePasseTemporaire');

      // 2. Créer le compte avec Firebase Auth + Firestore
      final result = await AdminCreationService.createLegitimateAdminCompagnie(
        email: email,
        password: motDePasseTemporaire,
        nom: nom,
        prenom: prenom,
        compagnieId: compagnieId,
        compagnieNom: compagnieNom,
        phone: phone,
      );

      if (result['success'] != true) {
        return {
          'success': false,
          'error': 'Échec création compte: ${result['error']}',
          'step': 'account_creation',
        };
      }

      debugPrint('[USER_CREATION_TEST] ✅ Compte créé - UID: ${result['uid']}');

      // 3. Envoyer l'email avec les identifiants
      final emailSent = await EmailNotificationService.sendAccountCreatedNotification(
        userEmail: email,
        userName: '$prenom $nom',
        temporaryPassword: motDePasseTemporaire,
        role: 'Administrateur de Compagnie',
        companyName: compagnieNom,
      );

      return {
        'success': true,
        'uid': result['uid'],
        'email': email,
        'temporaryPassword': motDePasseTemporaire,
        'emailSent': emailSent,
        'compagnieId': compagnieId,
        'compagnieNom': compagnieNom,
      };

    } catch (e) {
      debugPrint('[USER_CREATION_TEST] ❌ Erreur test: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
        'step': 'unknown',
      };
    }
  }

  /// 🧪 Tester la création complète d'un admin agence
  static Future<Map<String, dynamic>> testCreateAdminAgence({
    required String email,
    required String nom,
    required String prenom,
    required String compagnieId,
    required String agenceId,
    String? phone,
  }) async {
    try {
      debugPrint('[USER_CREATION_TEST] 🧪 Test création Admin Agence...');

      // 1. Générer un mot de passe temporaire
      final motDePasseTemporaire = _generateTemporaryPassword();

      // 2. Créer le compte
      final result = await AdminCreationService.createLegitimateAdminAgence(
        email: email,
        password: motDePasseTemporaire,
        nom: nom,
        prenom: prenom,
        compagnieId: compagnieId,
        agenceId: agenceId,
        phone: phone,
      );

      if (result['success'] != true) {
        return {
          'success': false,
          'error': 'Échec création compte: ${result['error']}',
          'step': 'account_creation',
        };
      }

      // 3. Envoyer l'email
      final emailSent = await EmailNotificationService.sendAccountCreatedNotification(
        userEmail: email,
        userName: '$prenom $nom',
        temporaryPassword: motDePasseTemporaire,
        role: 'Administrateur d\'Agence',
        companyName: 'Compagnie Test',
        agencyName: 'Agence Test',
      );

      return {
        'success': true,
        'uid': result['uid'],
        'email': email,
        'temporaryPassword': motDePasseTemporaire,
        'emailSent': emailSent,
        'compagnieId': compagnieId,
        'agenceId': agenceId,
      };

    } catch (e) {
      debugPrint('[USER_CREATION_TEST] ❌ Erreur test admin agence: $e');
      return {
        'success': false,
        'error': 'Erreur système: $e',
        'step': 'unknown',
      };
    }
  }

  /// 🔑 Générer un mot de passe temporaire pour les tests
  static String _generateTemporaryPassword() {
    final random = DateTime.now().millisecondsSinceEpoch;
    final randomString = random.toString().substring(8);
    return 'Test$randomString!';
  }

  /// 📊 Obtenir un rapport de test
  static Map<String, dynamic> getTestReport(Map<String, dynamic> result) {
    return {
      'success': result['success'] ?? false,
      'uid': result['uid'],
      'email': result['email'],
      'passwordGenerated': result['temporaryPassword'] != null,
      'emailSent': result['emailSent'] ?? false,
      'error': result['error'],
      'step': result['step'],
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 🧹 Nettoyer les données de test (à utiliser avec précaution)
  static Future<bool> cleanupTestData(String uid) async {
    try {
      debugPrint('[USER_CREATION_TEST] 🧹 Nettoyage données test pour UID: $uid');
      
      // Note: En production, cette méthode devrait être plus sécurisée
      // et ne supprimer que les données marquées comme test
      
      debugPrint('[USER_CREATION_TEST] ⚠️ Nettoyage non implémenté pour la sécurité');
      return false;
      
    } catch (e) {
      debugPrint('[USER_CREATION_TEST] ❌ Erreur nettoyage: $e');
      return false;
    }
  }
}
