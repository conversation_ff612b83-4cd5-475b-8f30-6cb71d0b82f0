name: constat_tunisie
description: "Application mobile pour la gestion des constats amiables d'accidents automobiles en Tunisie."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  cupertino_icons: ^1.0.2

  # Firebase - versions compatibles
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.6.7
  firebase_analytics: ^10.4.5
  firebase_crashlytics: ^3.3.5
  # firebase_app_check: ^0.2.1+8  # Temporairement désactivé pour éviter les problèmes
  cloud_functions: ^4.7.6

  # Notifications - AJOUT IMPORTANT
  flutter_local_notifications: ^17.0.0
  timezone: ^0.9.2

  # Signature électronique - AJOUT NÉCESSAIRE
  signature: ^5.4.0

  # Graphiques et charts - AJOUT POUR ADMIN DASHBOARD
  fl_chart: ^0.68.0

  # Autres dépendances
  uuid: ^4.0.0
  crypto: ^3.0.3
  
  shared_preferences: ^2.2.2
  flutter_native_splash: ^2.3.10
  lottie: ^3.0.0
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.7
  package_info_plus: ^4.0.0
  flutter_dotenv: ^5.1.0
  equatable: ^2.0.5
  flutter_riverpod: ^2.5.1

  # Export de données
  csv: ^6.0.0

  # Utiliser une version récente de http
  http: ^1.4.0

  # Email service universel
  emailjs: ^4.0.0
  
  logger: ^1.4.0
  flutter_cache_manager: ^3.3.1
  path_provider: ^2.1.0
  provider: ^6.0.0
  
  # Rétrograder google_fonts pour être compatible
  google_fonts: ^4.0.4
  
  image_picker: ^1.0.4
  url_launcher: ^6.1.14
  connectivity_plus: ^4.0.2
  flutter_launcher_icons: ^0.13.1
  smooth_page_indicator: ^1.1.0
  image: ^4.0.17

  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  pdf: ^3.10.4
  share_plus: ^7.1.0
  photo_view: ^0.14.0
  flutter_signature_pad: ^3.0.1
  printing: ^5.11.0
  geocoding: ^2.1.0
  intl: ^0.20.2
  audioplayers: ^5.2.0
  
  # IA SIMPLE et GRATUITE pour PFE (sans dépendances externes)
  # speech_to_text: ^6.6.0  # Temporairement désactivé pour éviter les erreurs de build
  
  google_sign_in: ^6.1.0
  path: ^1.8.3
  flutter_animate: ^4.2.0
  
  # Utiliser une alternative à image_cropper
  crop_your_image: ^0.7.5
  
  permission_handler: ^11.0.0
  
  # Ajout des dépendances manquantes pour la sérialisation JSON
  json_annotation: ^4.8.1
  
  # Ajout de dépendances utilitaires
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.0
  
  # Dépendances pour les fonctionnalités de constat
  flutter_tts: ^3.8.3
  record: ^5.0.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  
  # Ajout des dépendances de développement nécessaires
  build_runner: ^2.4.6
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/logos/
    - assets/icons/
    - assets/i18n/
    - assets/fonts/
    - assets/animations/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
