import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../services/simple_email_service.dart';

/// 📧 Widget pour afficher les emails en attente de traitement manuel
class PendingEmailsWidget extends StatefulWidget {
  const PendingEmailsWidget({super.key});

  @override
  State<PendingEmailsWidget> createState() => _PendingEmailsWidgetState();
}

class _PendingEmailsWidgetState extends State<PendingEmailsWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  Map<String, dynamic>? _emailStats;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadEmailStats();
  }

  /// 📊 Charger les statistiques d'email
  Future<void> _loadEmailStats() async {
    setState(() => _isLoading = true);
    try {
      final stats = await SimpleEmailService.getEmailStats();
      setState(() => _emailStats = stats);
    } catch (e) {
      debugPrint('[PENDING_EMAILS] ❌ Erreur chargement stats: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 📋 Copier les identifiants d'un email en attente
  Future<void> _copyCredentials(Map<String, dynamic> emailData) async {
    final credentials = '''
Email: ${emailData['to']}
Mot de passe: ${emailData['temporaryPassword']}
Rôle: ${emailData['role']}
${emailData['companyName'] != null ? 'Compagnie: ${emailData['companyName']}' : ''}

Instructions:
1. Transmettez ces identifiants à l'utilisateur
2. L'utilisateur doit changer son mot de passe à la première connexion
3. URL de connexion: https://constat-tunisie.com/login
''';

    await Clipboard.setData(ClipboardData(text: credentials));
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('📋 Identifiants copiés dans le presse-papier !'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  /// ✅ Marquer un email comme traité manuellement
  Future<void> _markAsProcessed(String docId) async {
    try {
      await _firestore.collection('pending_emails').doc(docId).update({
        'status': 'processed_manually',
        'processedAt': FieldValue.serverTimestamp(),
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Email marqué comme traité'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      _loadEmailStats(); // Recharger les stats
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête
            Row(
              children: [
                const Icon(Icons.email_outlined, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  '📧 Emails en Attente',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _loadEmailStats,
                  icon: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.refresh),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Statistiques
            if (_emailStats != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem(
                      'En attente',
                      _emailStats!['pending_manual']?.toString() ?? '0',
                      Colors.orange,
                    ),
                    _buildStatItem(
                      'Envoyés',
                      _emailStats!['successful_sends']?.toString() ?? '0',
                      Colors.green,
                    ),
                    _buildStatItem(
                      'Échecs',
                      _emailStats!['failed_sends']?.toString() ?? '0',
                      Colors.red,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
            ],
            
            // Liste des emails en attente
            Expanded(
              child: StreamBuilder<QuerySnapshot>(
                stream: _firestore
                    .collection('pending_emails')
                    .where('status', isEqualTo: 'pending_manual')
                    .orderBy('createdAt', descending: true)
                    .snapshots(),
                builder: (context, snapshot) {
                  if (snapshot.hasError) {
                    return Center(
                      child: Text('Erreur: ${snapshot.error}'),
                    );
                  }

                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  final docs = snapshot.data?.docs ?? [];

                  if (docs.isEmpty) {
                    return const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, size: 64, color: Colors.green),
                          SizedBox(height: 16),
                          Text(
                            '✅ Aucun email en attente !',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          Text('Tous les emails ont été traités.'),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    itemCount: docs.length,
                    itemBuilder: (context, index) {
                      final doc = docs[index];
                      final data = doc.data() as Map<String, dynamic>;
                      
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          leading: const CircleAvatar(
                            backgroundColor: Colors.orange,
                            child: Icon(Icons.person_add, color: Colors.white),
                          ),
                          title: Text(data['userName'] ?? 'Utilisateur'),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('📧 ${data['to']}'),
                              Text('🏷️ ${data['role']}'),
                              if (data['companyName'] != null)
                                Text('🏢 ${data['companyName']}'),
                              Text('🔑 ${data['temporaryPassword']}'),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                onPressed: () => _copyCredentials(data),
                                icon: const Icon(Icons.copy, color: Colors.blue),
                                tooltip: 'Copier les identifiants',
                              ),
                              IconButton(
                                onPressed: () => _markAsProcessed(doc.id),
                                icon: const Icon(Icons.check, color: Colors.green),
                                tooltip: 'Marquer comme traité',
                              ),
                            ],
                          ),
                          isThreeLine: true,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 📊 Construire un élément de statistique
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }
}
