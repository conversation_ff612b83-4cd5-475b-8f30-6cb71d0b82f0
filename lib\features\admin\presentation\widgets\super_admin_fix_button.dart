import 'package:flutter/material.dart';
import '../../services/super_admin_fix_service.dart';

/// 🔧 Bouton pour corriger les problèmes du Super Admin
class SuperAdminFixButton extends StatefulWidget {
  final VoidCallback? onFixComplete;
  
  const SuperAdminFixButton({
    super.key,
    this.onFixComplete,
  });

  @override
  State<SuperAdminFixButton> createState() => _SuperAdminFixButtonState();
}

class _SuperAdminFixButtonState extends State<SuperAdminFixButton> {
  bool _isFixing = false;
  bool _isDiagnosing = false;

  /// 🔧 Exécuter la correction
  Future<void> _runFix() async {
    setState(() => _isFixing = true);

    try {
      debugPrint('[SUPER_ADMIN_FIX_BUTTON] 🔧 Démarrage correction...');
      
      final result = await SuperAdminFixService.fixSuperAdminDocument();
      
      if (mounted) {
        if (result['success'] == true) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ ${result['message'] ?? 'Super Admin corrigé avec succès !'}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
          
          // Appeler le callback si fourni
          widget.onFixComplete?.call();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Erreur: ${result['error']}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur inattendue: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isFixing = false);
      }
    }
  }

  /// 🔍 Exécuter le diagnostic
  Future<void> _runDiagnosis() async {
    setState(() => _isDiagnosing = true);

    try {
      debugPrint('[SUPER_ADMIN_FIX_BUTTON] 🔍 Démarrage diagnostic...');
      
      // Afficher le diagnostic dans les logs
      await SuperAdminFixService.printFullDiagnosis();
      
      // Obtenir le diagnostic pour l'affichage
      final diagnosis = await SuperAdminFixService.diagnoseSuperAdminIssue();
      
      if (mounted) {
        _showDiagnosisDialog(diagnosis);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isDiagnosing = false);
      }
    }
  }

  /// 📊 Afficher le dialog de diagnostic
  void _showDiagnosisDialog(Map<String, dynamic> diagnosis) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.medical_services, color: Colors.blue),
            SizedBox(width: 8),
            Text('🔍 Diagnostic Super Admin'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Statut général
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: (diagnosis['can_create_users'] == true) 
                        ? Colors.green.shade100 
                        : Colors.red.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        (diagnosis['can_create_users'] == true) 
                            ? Icons.check_circle 
                            : Icons.error,
                        color: (diagnosis['can_create_users'] == true) 
                            ? Colors.green 
                            : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          (diagnosis['can_create_users'] == true)
                              ? '✅ Super Admin configuré correctement'
                              : '❌ Problèmes détectés avec Super Admin',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: (diagnosis['can_create_users'] == true) 
                                ? Colors.green.shade700 
                                : Colors.red.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Firebase Auth
                const Text(
                  '🔐 Firebase Auth',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                _buildInfoCard(diagnosis['firebase_auth'] as Map<String, dynamic>? ?? {}),
                
                const SizedBox(height: 16),
                
                // Firestore Document
                const Text(
                  '📄 Document Firestore',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                _buildInfoCard(diagnosis['firestore_document'] as Map<String, dynamic>? ?? {}),
                
                // Problèmes
                final issues = diagnosis['issues'] as List<dynamic>? ?? [];
                if (issues.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    '❌ Problèmes Détectés',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.red),
                  ),
                  const SizedBox(height: 8),
                  ...issues.map((issue) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• ', style: TextStyle(color: Colors.red)),
                        Expanded(child: Text(issue.toString())),
                      ],
                    ),
                  )),
                ],
                
                // Recommandations
                final recommendations = diagnosis['recommendations'] as List<dynamic>? ?? [];
                if (recommendations.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Text(
                    '💡 Recommandations',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.blue),
                  ),
                  const SizedBox(height: 8),
                  ...recommendations.map((rec) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('• ', style: TextStyle(color: Colors.blue)),
                        Expanded(child: Text(rec.toString())),
                      ],
                    ),
                  )),
                ],
              ],
            ),
          ),
        ),
        actions: [
          if ((diagnosis['issues'] as List<dynamic>? ?? []).isNotEmpty)
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _runFix();
              },
              icon: const Icon(Icons.build),
              label: const Text('Corriger'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  /// 🎨 Construire une carte d'information
  Widget _buildInfoCard(Map<String, dynamic> data) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: data.entries.map((entry) {
          if (entry.key == 'data') return const SizedBox.shrink(); // Skip raw data
          
          return Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(
                    '${entry.key}:',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value?.toString() ?? 'N/A',
                    style: TextStyle(
                      color: entry.value == true 
                          ? Colors.green 
                          : entry.value == false 
                              ? Colors.red 
                              : null,
                    ),
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton de diagnostic
        ElevatedButton.icon(
          onPressed: _isDiagnosing ? null : _runDiagnosis,
          icon: _isDiagnosing 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.medical_services),
          label: const Text('Diagnostic'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Bouton de correction
        ElevatedButton.icon(
          onPressed: _isFixing ? null : _runFix,
          icon: _isFixing 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.build),
          label: const Text('Corriger Super Admin'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
